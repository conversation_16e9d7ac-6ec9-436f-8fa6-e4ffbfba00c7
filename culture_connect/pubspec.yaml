name: culture_connect
description: "A mobile application connecting tourists with local guides for authentic cultural experiences."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  flutter_screenutil: ^5.9.0
  font_awesome_flutter: ^10.8.0
  smooth_page_indicator: ^1.1.0

  # Firebase
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  firebase_messaging: ^14.7.9
  firebase_analytics: ^10.7.4
  firebase_crashlytics: ^3.4.8
  cloud_firestore: ^4.13.6
  firebase_app_check: ^0.2.1+8
  firebase_storage: ^11.5.6

  # Auth & Security
  pinput: ^3.0.1
  crypto: ^3.0.3
  local_auth: ^2.1.7
  encrypt: ^5.0.3

  # State Management
  flutter_riverpod: ^2.4.9
  get_it: ^7.6.4
  injectable: ^2.3.2
  provider: ^6.1.1

  # Navigation
  auto_route: ^7.8.4
  go_router: ^6.5.9

  # Network & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  connectivity_plus: ^5.0.2

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^9.0.0
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  path: ^1.8.3

  # UI Components
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0
  lottie: ^2.7.0
  photo_view: ^0.14.0
  google_fonts: ^6.1.0
  intl: ^0.19.0
  scrollable_positioned_list: ^0.3.8
  timeline_tile: ^2.0.0

  # Charts & Data Visualization
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^28.1.39

  # Utilities
  permission_handler: ^11.1.0
  file_picker: ^9.0.2
  geolocator: ^10.1.0
  image_picker: ^1.0.5
  flutter_local_notifications: ^16.2.0
  google_maps_flutter: ^2.5.0
  google_maps_cluster_manager: ^3.1.0
  mime: ^1.0.4

  # New dependencies
  flutter_animate: ^4.3.0
  url_launcher: ^6.2.2
  share_plus: ^7.2.1
  path_provider: ^2.1.1
  flutter_dotenv: ^5.1.0
  speech_to_text: ^6.5.1
  just_audio: ^0.9.36
  record: ^5.0.4

  # Analytics & Monitoring
  firebase_performance: ^0.9.3+8
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0

  # Payment Provider SDKs
  flutter_stripe: ^11.5.0
  flutter_paystack: ^1.0.7
  qr_flutter: ^4.1.0
  flutter_pdfview: ^1.3.2
  jwt_decoder: ^2.0.1
  web_socket_channel: ^2.4.0

  # Connectivity & Offline Support
  timeago: ^3.6.0
  workmanager: ^0.5.2
  uuid: ^4.2.2
  battery_plus: ^4.1.0
  disk_space: ^0.2.1

  # AR dependencies
  camera: ^0.10.5+5
  google_mlkit_commons: ^0.6.1
  google_mlkit_text_recognition: ^0.11.0
  google_mlkit_language_id: ^0.9.0
  sensors_plus: ^4.0.2
  arcore_flutter_plugin: ^0.1.0
  vector_math: ^2.1.4
  http: ^1.1.0
  location: ^8.0.0
  equatable: ^2.0.7
  table_calendar: ^3.1.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.7
  injectable_generator: ^2.4.1
  auto_route_generator: ^7.3.2
  retrofit_generator: ^8.0.5
  hive_generator: ^2.0.1
  mockito: ^5.4.4
  build_test: ^2.2.1
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.8

  golden_toolkit: ^0.15.0
  network_image_mock: ^2.1.1
  mocktail: ^1.0.1
  fake_async: ^1.3.1

dependency_overrides:
  intl: ^0.19.0
  http: ^1.4.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/models/
    - .env

  # Using Google Fonts instead of local fonts

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/icons/app_icon_foreground.png"

flutter_native_splash:
  color: "#FFFFFF"
  image: assets/images/splash.png
  android: true
  ios: true
  web: false
