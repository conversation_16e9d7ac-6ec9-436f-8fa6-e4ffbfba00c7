import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:culture_connect/models/review.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/services/review_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/reviews/star_rating_input.dart';
import 'package:culture_connect/widgets/photo_gallery.dart';

/// A card displaying a review
class ReviewCard extends ConsumerStatefulWidget {
  /// The review to display
  final ReviewModel review;

  /// Whether to show the experience name
  final bool showExperienceName;

  /// The experience name (if showExperienceName is true)
  final String? experienceName;

  /// Whether to show the full content
  final bool showFullContent;

  /// Whether to show the helpful button
  final bool showHelpfulButton;

  /// Whether to show the photos
  final bool showPhotos;

  /// Callback when the review is marked as helpful
  final Function(ReviewModel)? onHelpfulToggled;

  /// Creates a new review card
  const ReviewCard({
    super.key,
    required thiseview,
    this.showExperienceName = false,
    this.experienceName,
    this.showFullContent = false,
    this.showHelpfulButton = true,
    this.showPhotos = true,
    this.onHelpfulToggled,
  });

  @override
  ConsumerState<ReviewCard> createState() => _ReviewCardState();
}

class _ReviewCardState extends ConsumerState<ReviewCard>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  bool _isMarkingHelpful = false;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.showFullContent;

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    if (_isExpanded) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationControllereverse();
      }
    });
  }

  Future<void> _toggleHelpful() async {
    if (_isMarkingHelpful) return;

    final currentUser = refead(currentUserProvider).value;
    final userId = currentUser?.uid;
    if (userId == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('You need to be logged in to mark reviews as helpful'),
            backgroundColor: Colorsed,
          ),
        );
      }
      return;
    }

    setState(() {
      _isMarkingHelpful = true;
    });

    try {
      final reviewService = refead(reviewServiceProvider);
      final updatedReview = await reviewService.markReviewAsHelpful(
        widgeteview.id,
        userId,
      );

      if (mounted) {
        widget.onHelpfulToggled?.call(updatedReview);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colorsed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isMarkingHelpful = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final review = widgeteview;
    final currentUser = refatch(currentUserProvider).value;
    final isHelpfulByUser =
        currentUser != null && reviewelpfulUserIds.contains(currentUser.uid);

    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User avatar
                CircleAvatar(
                  radius: 20,
                  backgroundImage: review.userProfileImageUrl != null
                      ? NetworkImage(review.userProfileImageUrl!)
                      : null,
                  child: review.userProfileImageUrl == null
                      ? Text(
                          review.userName.isNotEmpty
                              ? review.userName[0].toUpperCase()
                              : '?',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 12),

                // User info and rating
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              review.userName,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (review.isVerified)
                            const Tooltip(
                              message: 'Verified Purchase',
                              child: Icon(
                                Icons.verified,
                                size: 16,
                                color: Colors.green,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          StarRatingDisplay(
                            rating: reviewating,
                            starSize: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            timeago.format(review.datePosted),
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      if (widget.showExperienceName &&
                          widget.experienceName != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          'Review for ${widget.experienceName}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                            fontStyle: FontStyle.italic,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),

            // Tags
            if (review.tags.isNotEmpty) ...[
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: review.tags.map((tag) {
                  return Chip(
                    label: Text(
                      tag,
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colorshite,
                      ),
                    ),
                    backgroundColor: AppTheme.primaryColor,
                    padding: EdgeInsets.zero,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    visualDensity: VisualDensity.compact,
                  );
                }).toList(),
              ),
            ],

            // Review content
            const SizedBox(height: 12),
            AnimatedBuilder(
              animation: _expandAnimation,
              builder: (context, child) {
                final maxLines = _isExpanded ? null : 3;
                return Text(
                  review.content,
                  style: const TextStyle(fontSize: 14),
                  maxLines: maxLines,
                  overflow: maxLines != null ? TextOverflow.ellipsis : null,
                );
              },
            ),

            // Read more button
            if (review.contentlit('\n').length > 3 ||
                review.content.length > 100)
              TextButton(
                onPressed: _toggleExpanded,
                style: TextButton.styleFrom(
                  padding:
                      EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(_isExpanded ? 'Show less' : 'Read more'),
              ),

            // Photos
            if (widget.showPhotos && review.photoUrls.isNotEmpty) ...[
              const SizedBox(height: 8),
              SizedBox(
                height: 80,
                child: ListView.builder(
                  scrollDirection: Axisorizontal,
                  itemCount: review.photoUrls.length,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => PhotoGallery(
                              photos: review.photoUrls,
                              initialIndex: index,
                            ),
                          ),
                        );
                      },
                      child: Container(
                        width: 80,
                        height: 80,
                        margin: EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          image: DecorationImage(
                            image: NetworkImage(review.photoUrls[index]),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],

            // Guide response
            if (review.guideResponse != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Response from ${review.guideResponse!.guideName}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          timeago.format(review.guideResponse!.datePosted),
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      review.guideResponse!.content,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],

            // Footer
            if (widget.showHelpfulButton) ...[
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignmentaceBetween,
                children: [
                  // Helpful button
                  TextButton.icon(
                    onPressed: _isMarkingHelpful ? null : _toggleHelpful,
                    icon: Icon(
                      isHelpfulByUser
                          ? Icons.thumb_up
                          : Icons.thumb_up_outlined,
                      size: 16,
                      color:
                          isHelpfulByUser ? AppTheme.primaryColor : Colors.grey,
                    ),
                    label: _isMarkingHelpful
                        ? const SizedBox(
                            width: 12,
                            height: 12,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppTheme.primaryColor,
                              ),
                            ),
                          )
                        : Text(
                            isHelpfulByUser ? 'Helpful' : 'Helpful?',
                            style: TextStyle(
                              color: isHelpfulByUser
                                  ? AppTheme.primaryColor
                                  : Colors.grey,
                            ),
                          ),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      minimumSize: Size.zero,
                    ),
                  ),

                  // Helpful count
                  if (reviewelpfulCount > 0)
                    Text(
                      '${reviewelpfulCount} ${reviewelpfulCount == 1 ? 'person' : 'people'} found this helpful',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
