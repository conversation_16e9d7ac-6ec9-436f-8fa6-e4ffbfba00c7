import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/review.dart';
import 'package:culture_connect/services/review_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/reviews/review_card.dart';
import 'package:culture_connect/widgets/reviews/review_submission_form.dart';
import 'package:culture_connect/widgets/reviews/star_rating_input.dart';

/// A widget for displaying a list of reviews
class ReviewList extends ConsumerStatefulWidget {
  /// The experience to show reviews for
  final Experience experience;

  /// The maximum number of reviews to show
  final int? maxReviews;

  /// Whether to show the "See All Reviews" button
  final bool showSeeAllButton;

  /// Whether to show the "Write a Review" button
  final bool showWriteReviewButton;

  /// Whether to show the rating distribution
  final bool showRatingDistribution;

  /// Whether to show the sort options
  final bool showSortOptions;

  /// Creates a new review list widget
  const ReviewList({
    super.key,
    required this.experience,
    this.maxReviews,
    this.showSeeAllButton = true,
    this.showWriteReviewButton = true,
    this.showRatingDistribution = true,
    this.showSortOptions = true,
  });

  @override
  ConsumerState<ReviewList> createState() => _ReviewListState();
}

class _ReviewListState extends ConsumerState<ReviewList> {
  ReviewSortOption _sortOption = ReviewSortOption.newest;

  @override
  void initState() {
    super.initState();
    // Set the initial sort option in the provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      refead(reviewSortOptionProvider.notifier).state = _sortOption;
    });
  }

  void _showReviewForm() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colorshite,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            left: 16,
            right: 16,
            top: 16,
            bottom: MediaQuery.of(context).viewInsets.bottom + 16,
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  mainAxisAlignment: MainAxisAlignmentaceBetween,
                  children: [
                    const Text(
                      'Write a Review',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Experience info
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        widget.experience.imageUrl,
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.experience.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.experience.location,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Review form
                ReviewSubmissionForm(
                  experience: widget.experience,
                  onSubmitted: (success) {
                    if (success) {
                      // Refresh the reviews
                      final _ = refefresh(
                          experienceReviewsProvider(widget.experience.id));
                    }
                  },
                ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAllReviews() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AllReviewsScreen(experience: widget.experience),
      ),
    );
  }

  void _updateSortOption(ReviewSortOption option) {
    setState(() {
      _sortOption = option;
    });
    refead(reviewSortOptionProvider.notifier).state = option;
  }

  @override
  Widget build(BuildContext context) {
    final reviewsAsync =
        refatch(sortedExperienceReviewsProvider(widget.experience.id));
    final ratingDistributionAsync =
        refatch(ratingDistributionProvider(widget.experience.id));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with rating summary
        Row(
          children: [
            const Text(
              'Reviews',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            if (widget.showWriteReviewButton)
              TextButton.icon(
                onPressed: _showReviewForm,
                icon: const Icon(
                  Iconsate_review,
                  size: 16,
                  color: Colors.blue,
                ),
                label: const Text(
                  'Write a Review',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),

        const SizedBox(height: 16),

        // Rating summary
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Average rating
            Column(
              children: [
                Text(
                  widget.experienceating.toStringAsFixed(1),
                  style: const TextStyle(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                StarRatingDisplay(
                  rating: widget.experienceating,
                  starSize: 16,
                ),
                const SizedBox(height: 4),
                Text(
                  '${widget.experienceeviewCount} ${widget.experienceeviewCount == 1 ? 'review' : 'reviews'}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),

            const SizedBox(width: 24),

            // Rating distribution
            if (widget.showRatingDistribution)
              Expanded(
                child: ratingDistributionAsynchen(
                  data: (distribution) {
                    return Column(
                      children: List.generate(5, (index) {
                        final starCount = 5 - index;
                        final count = distribution[starCount] ?? 0;
                        final total = widget.experienceeviewCount;
                        final percent = total > 0 ? count / total : 0.0;

                        return Padding(
                          padding: EdgeInsets.only(bottom: 4),
                          child: Row(
                            children: [
                              Text(
                                '$starCount',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                              const SizedBox(width: 4),
                              const Icon(
                                Icons.star,
                                size: 12,
                                color: Colors
                                    .amber, // Using standard amber color instead of AppTheme.starColor
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(4),
                                  child: LinearProgressIndicator(
                                    value: percent,
                                    backgroundColor: Colors.grey.shade200,
                                    valueColor:
                                        const AlwaysStoppedAnimation<Color>(
                                      AppTheme.primaryColor,
                                    ),
                                    minHeight: 8,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                count.toString(),
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    );
                  },
                  loading: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  error: (_, __) => const SizedBox(),
                ),
              ),
          ],
        ),

        // Sort options
        if (widget.showSortOptions) ...[
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axisorizontal,
            child: Row(
              children: ReviewSortOption.values.map((option) {
                final isSelected = _sortOption == option;
                return Padding(
                  padding: EdgeInsets.only(right: 8),
                  child: ChoiceChip(
                    label: Text(option.displayName),
                    selected: isSelected,
                    onSelected: (_) => _updateSortOption(option),
                    backgroundColor: Colors.grey.shade100,
                    selectedColor: AppTheme.primaryColorithAlpha(51),
                    labelStyle: TextStyle(
                      color: isSelected ? AppTheme.primaryColor : Colors.black,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Reviews list
        reviewsAsynchen(
          data: (reviews) {
            if (reviews.isEmpty) {
              return Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 24),
                  child: Column(
                    children: [
                      const Icon(
                        Iconsate_review_outlined,
                        size: 48,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'No reviews yet',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                      if (widget.showWriteReviewButton) ...[
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _showReviewForm,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colorshite,
                          ),
                          child: const Text('Be the first to review'),
                        ),
                      ],
                    ],
                  ),
                ),
              );
            }

            final displayedReviews =
                widget.maxReviews != null && reviews.length > widget.maxReviews!
                    ? reviews.sublist(0, widget.maxReviews)
                    : reviews;

            return Column(
              children: [
                ...displayedReviews.map((review) => ReviewCard(
                      review: review,
                      onHelpfulToggled: (_) {
                        // Refresh the reviews
                        final _ = refefresh(
                            experienceReviewsProvider(widget.experience.id));
                      },
                    )),
                if (widget.showSeeAllButton &&
                    widget.maxReviews != null &&
                    reviews.length > widget.maxReviews!) ...[
                  const SizedBox(height: 16),
                  Center(
                    child: OutlinedButton(
                      onPressed: _showAllReviews,
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppTheme.primaryColor,
                        side: const BorderSide(color: AppTheme.primaryColor),
                        padding: EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'See All ${reviews.length} Reviews',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            );
          },
          loading: () => const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 24),
              child: CircularProgressIndicator(),
            ),
          ),
          error: (error, stackTrace) => Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 24),
              child: Text(
                'Error loading reviews: $error',
                style: const TextStyle(
                  color: Colorsed,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Screen for displaying all reviews for an experience
class AllReviewsScreen extends ConsumerWidget {
  /// The experience to show reviews for
  final Experience experience;

  /// Creates a new all reviews screen
  const AllReviewsScreen({
    super.key,
    required this.experience,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Reviews for ${experience.title}'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: ReviewList(
          experience: experience,
          showSeeAllButton: false,
        ),
      ),
    );
  }
}
