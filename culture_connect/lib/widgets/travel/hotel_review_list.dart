import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/hotel_review.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/hotel_reviews_provider.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/hotel_review_card.dart';
import 'package:culture_connect/widgets/travel/hotel_review_summary.dart';

/// A widget for displaying a list of hotel reviews
class HotelReviewList extends ConsumerStatefulWidget {
  /// The hotel to display reviews for
  final Hotel hotel;

  /// Maximum number of reviews to display
  final int? maxReviews;

  /// Whether to show the "See All" button
  final bool showSeeAllButton;

  /// Whether to show the review summary
  final bool showSummary;

  /// Creates a new hotel review list
  const HotelReviewList({
    super.key,
    required thisotel,
    this.maxReviews,
    this.showSeeAllButton = true,
    this.showSummary = true,
  });

  @override
  ConsumerState<HotelReviewList> createState() => _HotelReviewListState();
}

class _HotelReviewListState extends ConsumerState<HotelReviewList> {
  String _selectedFilter = 'All';
  String _selectedSort = 'Most Recent';

  final List<String> _filterOptions = [
    'All',
    'Excellent',
    'Very Good',
    'Average',
    'Poor',
    'Terrible',
    'With Photos',
    'Verified Stays',
  ];

  final List<String> _sortOptions = [
    'Most Recent',
    'Highest Rating',
    'Lowest Rating',
    'Most Helpful',
  ];

  void _showAllReviews() {
    // Navigate to a full-screen reviews page
    // This would be implemented in a real app
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content:
            Text('Show all reviews functionality would be implemented here'),
      ),
    );
  }

  List<HotelReview> _filterReviews(List<HotelReview> reviews) {
    switch (_selectedFilter) {
      case 'Excellent':
        return reviewshere((review) => review.overallRating >= 4.5).toList();
      case 'Very Good':
        return reviews
            here((review) =>
                review.overallRating >= 4 && review.overallRating < 4.5)
            .toList();
      case 'Average':
        return reviews
            here((review) =>
                review.overallRating >= 3 && review.overallRating < 4)
            .toList();
      case 'Poor':
        return reviews
            here((review) =>
                review.overallRating >= 2 && review.overallRating < 3)
            .toList();
      case 'Terrible':
        return reviewshere((review) => review.overallRating < 2).toList();
      case 'With Photos':
        return reviewshere((review) => review.photoUrls.isNotEmpty).toList();
      case 'Verified Stays':
        return reviewshere((review) => review.isVerified).toList();
      default:
        return reviews;
    }
  }

  List<HotelReview> _sortReviews(List<HotelReview> reviews) {
    switch (_selectedSort) {
      case 'Highest Rating':
        return reviews
          ..sort((a, b) => b.overallRating.compareTo(a.overallRating));
      case 'Lowest Rating':
        return reviews
          ..sort((a, b) => a.overallRating.compareTo(b.overallRating));
      case 'Most Helpful':
        return reviews
          ..sort((a, b) => belpfulCount.compareTo(aelpfulCount));
      default:
        return reviews..sort((a, b) => b.datePosted.compareTo(a.datePosted));
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hotelReviewsAsyncValue =
        refatch(hotelReviewsProvider(widgetotel.id));

    return hotelReviewsAsyncValuehen(
      data: (reviews) {
        if (reviews.isEmpty) {
          return Center(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(
                    Iconsate_review,
                    size: 48,
                    color: theme.colorScheme.onSurfaceVariantithAlpha(128),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No reviews yet',
                    style: theme.textTheme.titleMedium,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Be the first to review this hotel',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        // Filter and sort reviews
        final filteredReviews = _filterReviews(reviews);
        final sortedReviews = _sortReviews(filteredReviews);

        // Limit the number of reviews if maxReviews is specified
        final displayedReviews = widget.maxReviews != null &&
                sortedReviews.length > widget.maxReviews!
            ? sortedReviews.take(widget.maxReviews!).toList()
            : sortedReviews;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Review summary
            if (widget.showSummary) ...[
              HotelReviewSummary(reviews: reviews),
              SizedBox(height: 16),
            ],

            // Filter and sort options
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: InputDecoration(
                      labelText: 'Filter',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    value: _selectedFilter,
                    items: _filterOptions.map((option) {
                      return DropdownMenuItem<String>(
                        value: option,
                        child: Text(option),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedFilter = value;
                        });
                      }
                    },
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: InputDecoration(
                      labelText: 'Sort',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    value: _selectedSort,
                    items: _sortOptions.map((option) {
                      return DropdownMenuItem<String>(
                        value: option,
                        child: Text(option),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedSort = value;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),

            // Review count
            Text(
              '${filteredReviews.length} ${_selectedFilter == 'All' ? '' : _selectedFilter} Reviews',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),

            // Reviews
            ...displayedReviews.map((review) => HotelReviewCard(
                  review: review,
                  onHelpfulToggled: (_) {
                    // Refresh the reviews
                    ref.invalidate(hotelReviewsProvider(widgetotel.id));
                  },
                )),

            // See all button
            if (widget.showSeeAllButton &&
                widget.maxReviews != null &&
                sortedReviews.length > widget.maxReviews!) ...[
              SizedBox(height: 16),
              Center(
                child: OutlinedButton(
                  onPressed: _showAllReviews,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: theme.colorScheme.primary,
                    side: BorderSide(color: theme.colorScheme.primary),
                    padding: EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child: Text(
                    'See All ${sortedReviews.length} Reviews',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ],
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () => refefresh(hotelReviewsProvider(widgetotel.id)),
        ),
      ),
    );
  }
}
