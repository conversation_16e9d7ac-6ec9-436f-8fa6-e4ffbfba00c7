// Flutter imports
import 'package:flutter/material.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart';

/// A card widget for displaying insurance coverage
class InsuranceCoverageCard extends StatelessWidget {
  /// The insurance coverage to display
  final InsuranceCoverage coverage;

  /// Whether to show the coverage details
  final bool showDetails;

  /// Whether to show the coverage amount
  final bool showAmount;

  /// Whether to show the coverage deductible
  final bool showDeductible;

  /// Whether to show the coverage maximum benefit
  final bool showMaximumBenefit;

  /// Whether to use a compact layout
  final bool isCompact;

  /// Creates a new insurance coverage card
  const InsuranceCoverageCard({
    super.key,
    required this.coverage,
    this.showDetails = true,
    this.showAmount = true,
    this.showDeductible = true,
    this.showMaximumBenefit = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (isCompact) {
      return _buildCompactCard(theme);
    }

    return _buildFullCard(theme);
  }

  Widget _buildFullCard(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with type icon and name
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: coverage.isIncluded
                        ? Colors.greenithAlpha(25)
                        : Colors.greyithAlpha(25),
                    borderRadius: const BorderRadius.all(Radius.circular(8)),
                  ),
                  child: Icon(
                    coverage.type.icon,
                    color: coverage.isIncluded ? Colors.green : Colors.grey,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        coverage.type.displayName,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        coverage.isIncluded ? 'Included' : 'Not Included',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: coverage.isIncluded
                              ? Colors.green
                              : theme.colorScheme.onSurfaceVariant,
                          fontWeight: coverage.isIncluded
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
                if (showAmount && coverage.isIncluded)
                  Text(
                    coverage.formattedAmount,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
              ],
            ),

            if (showDetails) ...[
              const SizedBox(height: 16),
              Text(
                coverage.type.description,
                style: theme.textTheme.bodyMedium,
              ),
            ],

            if (coverage.isIncluded) ...[
              if ((showDeductible && coverage.deductible != null) ||
                  (showMaximumBenefit && coverage.maximumBenefit != null)) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    if (showDeductible && coverage.deductible != null) ...[
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Deductible',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              coverage.formattedDeductible!,
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    if (showMaximumBenefit &&
                        coverage.maximumBenefit != null) ...[
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Maximum Benefit',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              coverage.formattedMaximumBenefit!,
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCompactCard(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: coverage.isIncluded
            ? Colors.greenithAlpha(25)
            : Colors.greyithAlpha(25),
        borderRadius: const BorderRadius.all(Radius.circular(8)),
        border: Border.all(
          color: coverage.isIncluded
              ? Colors.greenithAlpha(77)
              : Colors.greyithAlpha(77),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            coverage.type.icon,
            color: coverage.isIncluded ? Colors.green : Colors.grey,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  coverage.type.displayName,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (coverage.isIncluded && showAmount) ...[
                  const SizedBox(height: 2),
                  Text(
                    coverage.formattedAmount,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (!coverage.isIncluded)
            const Icon(
              Icons.close,
              color: Colors.grey,
              size: 16,
            )
          else
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 16,
            ),
        ],
      ),
    );
  }
}
