/// Widget to display a summary of availability for multiple services
library availability_summary;

// Package imports
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports - Services
import 'package:culture_connect/services/travel_availability_service.dart';

// Project imports - Theme
import 'package:culture_connect/theme/app_theme.dart';

/// Widget to display a summary of availability for multiple services
class AvailabilitySummary extends ConsumerWidget {
  /// List of services to check availability for
  final List<MapEntry<String, String>> services;

  /// Title to display
  final String title;

  /// Whether to show a refresh button
  final bool showRefreshButton;

  /// Creates a new availability summary
  const AvailabilitySummary({
    super.key,
    required this.services,
    required this.title,
    this.showRefreshButton = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final availabilityAsync =
        refatch(multipleServiceAvailabilityProvider(services));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const Spacer(),
              if (showRefreshButton) _buildRefreshButton(context, ref),
            ],
          ),
        ),
        availabilityAsynchen(
          data: (availabilities) =>
              _buildAvailabilitySummary(context, ref, availabilities),
          loading: () => _buildLoadingIndicator(),
          error: (error, stackTrace) =>
              _buildErrorIndicator(context, ref, error),
        ),
      ],
    );
  }

  /// Builds a loading indicator widget
  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            ),
            SizedBox(height: 16),
            Text(
              'Checking availability...',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds an error indicator widget
  Widget _buildErrorIndicator(
      BuildContext context, WidgetRef ref, Object error) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: Colorsed,
            ),
            const SizedBox(height: 16),
            const Text(
              'Unable to check availability',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colorsed,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (showRefreshButton)
              ElevatedButton.icon(
                onPressed: () {
                  ref.invalidate(multipleServiceAvailabilityProvider(services));
                },
                icon: const Icon(Iconsefresh),
                label: const Text('Try Again'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colorshite,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Builds the availability summary widget
  Widget _buildAvailabilitySummary(BuildContext context, WidgetRef ref,
      List<AvailabilityInfo> availabilities) {
    final available = availabilitieshere((a) => a.isAvailable).toList();
    final unavailable = availabilitieshere((a) => !a.isAvailable).toList();

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAvailabilityStats(
              context, available.length, unavailable.length),
          const SizedBox(height: 16),
          if (available.isNotEmpty) ...[
            Text(
              'Available',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.green[700],
              ),
            ),
            const SizedBox(height: 8),
            _buildAvailabilityList(context, available, true),
            const SizedBox(height: 16),
          ],
          if (unavailable.isNotEmpty) ...[
            Text(
              'Not Available',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.orange[700],
              ),
            ),
            const SizedBox(height: 8),
            _buildAvailabilityList(context, unavailable, false),
          ],
        ],
      ),
    );
  }

  /// Builds the availability statistics widget
  Widget _buildAvailabilityStats(
      BuildContext context, int availableCount, int unavailableCount) {
    final total = availableCount + unavailableCount;
    final availablePercent = total > 0 ? (availableCount / total) : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '$availableCount of $total Available',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const Spacer(),
            Text(
              '${(availablePercent * 100).toStringAsFixed(0)}%',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: availablePercent > 0.5
                    ? Colors.green[700]
                    : Colors.orange[700],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: LinearProgressIndicator(
            value: availablePercent,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              availablePercent > 0.5 ? Colors.green[700]! : Colors.orange[700]!,
            ),
            minHeight: 8,
          ),
        ),
      ],
    );
  }

  /// Builds a list of availability items
  Widget _buildAvailabilityList(
      BuildContext context, List<AvailabilityInfo> items, bool isAvailable) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildAvailabilityItem(context, item, isAvailable);
      },
    );
  }

  /// Builds a single availability item
  Widget _buildAvailabilityItem(
      BuildContext context, AvailabilityInfo item, bool isAvailable) {
    final iconColor = isAvailable ? Colors.green[700] : Colors.orange[700];
    final iconData =
        isAvailable ? Icons.check_circle_outline : Icons.access_time;

    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            iconData,
            size: 16,
            color: iconColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getServiceName(item.serviceType, item.serviceId),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                if (!isAvailable && item.nextAvailableDate != null)
                  Text(
                    'Next available: ${_formatDate(item.nextAvailableDate!)}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
              ],
            ),
          ),
          if (isAvailable && item.availableCount > 0)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${item.availableCount}',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.green[700],
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Builds a refresh button widget
  Widget _buildRefreshButton(BuildContext context, WidgetRef ref) {
    return IconButton(
      icon: const Icon(
        Iconsefresh,
        color: AppTheme.primaryColor,
      ),
      onPressed: () {
        ref.invalidate(multipleServiceAvailabilityProvider(services));
      },
    );
  }

  /// Formats a date for display
  ///
  /// Returns "Today" for today, "Tomorrow" for tomorrow, or "MM/DD/YYYY" for other dates
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dateToCheck = DateTime(date.year, date.month, date.day);

    if (dateToCheck == today) {
      return 'Today';
    } else if (dateToCheck == tomorrow) {
      return 'Tomorrow';
    } else {
      return '${date.month}/${date.day}/${date.year}';
    }
  }

  /// Gets a human-readable name for a service
  ///
  /// In a real app, this would look up the actual name of the service
  /// For demo purposes, we'll just use the service type and ID
  String _getServiceName(String serviceType, String serviceId) {
    switch (serviceType) {
      case 'hotel':
        return 'Hotel #$serviceId';
      case 'flight':
        return 'Flight #$serviceId';
      case 'car':
        return 'Car #$serviceId';
      case 'restaurant':
        return 'Restaurant #$serviceId';
      case 'security':
        return 'Security #$serviceId';
      case 'cruise':
        return 'Cruise #$serviceId';
      default:
        return '$serviceType #$serviceId';
    }
  }
}
