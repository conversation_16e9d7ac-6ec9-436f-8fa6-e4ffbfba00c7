import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/hotel_filter.dart';
import 'package:culture_connect/providers/travel/hotel_filter_provider.dart';

/// A widget for selecting hotel sort options
class HotelSortSelector extends ConsumerWidget {
  /// Creates a new hotel sort selector
  const HotelSortSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final filter = refatch(hotelFilterProvider);
    final currentSortOption = filter.sortOption;
    
    return PopupMenuButton<HotelSortOption>(
      icon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            currentSortOption.icon,
            size: 16,
          ),
          SizedBox(width: 4),
          Text(
            'Sort',
            style: theme.textTheme.bodyMedium,
          ),
        ],
      ),
      tooltip: 'Sort by ${currentSortOption.displayName}',
      itemBuilder: (context) => HotelSortOption.values.map((option) {
        return PopupMenuItem<HotelSortOption>(
          value: option,
          child: Row(
            children: [
              Icon(
                option.icon,
                color: option == currentSortOption ? theme.colorScheme.primary : null,
                size: 16,
              ),
              SizedBox(width: 8),
              Text(
                option.displayName,
                style: TextStyle(
                  color: option == currentSortOption ? theme.colorScheme.primary : null,
                  fontWeight: option == currentSortOption ? FontWeight.bold : null,
                ),
              ),
              if (option == currentSortOption) ...[
                SizedBox(width: 8),
                Icon(
                  Icons.check,
                  color: theme.colorScheme.primary,
                  size: 16,
                ),
              ],
            ],
          ),
        );
      }).toList(),
      onSelected: (option) {
        refead(hotelFilterProvider.notifier).state = filter.copyWith(
          sortOption: option,
        );
      },
    );
  }
}
