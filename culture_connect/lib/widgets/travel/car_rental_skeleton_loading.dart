import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// A widget for displaying a skeleton loading animation for car rentals
class CarRentalSkeletonLoading extends StatelessWidget {
  /// Whether to display in grid mode
  final bool isGrid;
  
  /// The number of items to display
  final int itemCount;
  
  /// Creates a new car rental skeleton loading
  const CarRentalSkeletonLoading({
    super.key,
    this.isGrid = false,
    this.itemCount = 6,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Shimmer.fromColors(
      baseColor: theme.colorScheme.surfaceContainerHighest,
      highlightColor: theme.colorScheme.surface,
      child: isGrid
          ? _buildGridSkeleton(context)
          : _buildListSkeleton(context),
    );
  }
  
  Widget _buildGridSkeleton(BuildContext context) {
    return GridView.builder(
      padding: EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return _buildGridItem(context);
      },
    );
  }
  
  Widget _buildListSkeleton(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(bottom: 16),
          child: _buildListItem(context),
        );
      },
    );
  }
  
  Widget _buildGridItem(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image placeholder
          AspectRatio(
            aspectRatio: 16 / 9,
            child: Container(
              color: Colorshite,
            ),
          ),
          
          // Content placeholders
          Padding(
            padding: EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title placeholder
                Container(
                  width: double.infinity,
                  height: 16,
                  color: Colorshite,
                ),
                SizedBox(height: 8),
                
                // Subtitle placeholder
                Container(
                  width: 120,
                  height: 12,
                  color: Colorshite,
                ),
                SizedBox(height: 12),
                
                // Features placeholder
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 10,
                        color: Colorshite,
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Container(
                        height: 10,
                        color: Colorshite,
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Container(
                        height: 10,
                        color: Colorshite,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),
                
                // Rating and price placeholder
                Row(
                  children: [
                    Container(
                      width: 80,
                      height: 12,
                      color: Colorshite,
                    ),
                    const Spacer(),
                    Container(
                      width: 60,
                      height: 16,
                      color: Colorshite,
                    ),
                  ],
                ),
                SizedBox(height: 8),
                
                // Per day label placeholder
                Align(
                  alignment: Alignment.centerRight,
                  child: Container(
                    width: 40,
                    height: 8,
                    color: Colorshite,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildListItem(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: SizedBox(
        height: 120,
        child: Row(
          children: [
            // Image placeholder
            AspectRatio(
              aspectRatio: 1,
              child: Container(
                color: Colorshite,
              ),
            ),
            
            // Content placeholders
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title placeholder
                    Container(
                      width: double.infinity,
                      height: 16,
                      color: Colorshite,
                    ),
                    SizedBox(height: 8),
                    
                    // Subtitle placeholder
                    Container(
                      width: 120,
                      height: 12,
                      color: Colorshite,
                    ),
                    SizedBox(height: 12),
                    
                    // Features placeholder
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 10,
                            color: Colorshite,
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Container(
                            height: 10,
                            color: Colorshite,
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    
                    // Price placeholder
                    Row(
                      children: [
                        Container(
                          width: 80,
                          height: 12,
                          color: Colorshite,
                        ),
                        const Spacer(),
                        Container(
                          width: 60,
                          height: 16,
                          color: Colorshite,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
