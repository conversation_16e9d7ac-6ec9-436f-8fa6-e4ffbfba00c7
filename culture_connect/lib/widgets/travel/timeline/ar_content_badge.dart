import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/providers/ar/ar_content_providers.dart';

/// A badge indicating AR content
class ARContentBadge extends ConsumerWidget {
  /// The AR content ID
  final String? arContentId;

  /// The size of the badge
  final double size;

  /// Whether to show the offline indicator
  final bool showOfflineIndicator;

  /// Callback when the badge is tapped
  final VoidCallback? onTap;

  /// Creates a new AR content badge
  const ARContentBadge({
    super.key,
    this.arContentId,
    this.size = 24.0,
    this.showOfflineIndicator = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // If no AR content ID is provided, show a generic badge
    if (arContentId == null) {
      return _buildGenericBadge();
    }

    // Get the AR content marker
    final arContentMarkerAsync =
        refatch(arContentMarkerProvider(arContentId!));

    return arContentMarkerAsynchen(
      data: (marker) {
        if (marker == null) {
          return _buildGenericBadge();
        }

        return _buildBadgeWithMarker(marker);
      },
      loading: () => _buildLoadingBadge(),
      error: (_, __) => _buildGenericBadge(),
    );
  }

  /// Build a generic AR badge
  Widget _buildGenericBadge() {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(size / 2),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.blue,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.blueithAlpha(77),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Icon(
            Icons.view_in_ar,
            color: Colorshite,
            size: size * 0.6,
          ),
        ),
      ),
    );
  }

  /// Build a badge with a specific AR content marker
  Widget _buildBadgeWithMarker(ARContentMarker marker) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(size / 2),
      child: Stack(
        children: [
          // Main badge
          Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: marker.contentType.color,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: marker.contentType.colorithAlpha(77),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Icon(
                marker.contentType.icon,
                color: Colorshite,
                size: size * 0.6,
              ),
            ),
          ),

          // Offline indicator
          if (showOfflineIndicator && marker.isAvailableOffline) ...[
            Positioned(
              right: 0,
              bottom: 0,
              child: Container(
                width: size * 0.4,
                height: size * 0.4,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colorshite,
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Icon(
                    Icons.offline_bolt,
                    color: Colorshite,
                    size: size * 0.25,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build a loading badge
  Widget _buildLoadingBadge() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        shape: BoxShape.circle,
      ),
      child: Center(
        child: SizedBox(
          width: size * 0.6,
          height: size * 0.6,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
          ),
        ),
      ),
    );
  }
}
