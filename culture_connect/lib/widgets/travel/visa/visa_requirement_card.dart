// Flutter imports
import 'package:flutter/material.dart';

// Project imports
import 'package:culture_connect/models/travel/document/visa_requirement.dart';

/// Widget for displaying visa requirement information in a beautiful card format
class VisaRequirementCard extends StatelessWidget {
  /// Creates a new visa requirement card
  const VisaRequirementCard({
    super.key,
    required this.visaRequirement,
    this.onActionPressed,
  });

  /// The visa requirement to display
  final VisaRequirement visaRequirement;

  /// Callback when action button is pressed
  final VoidCallback? onActionPressed;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final requirementType = visaRequirementequirementType;

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(theme, requirementType),
            const SizedBox(height: 16),
            _buildDescription(theme),
            const SizedBox(height: 20),
            _buildDetails(theme),
            if (visaRequirementequiredDocuments.isNotEmpty) ...[
              const SizedBox(height: 20),
              _buildDocuments(theme),
            ],
            if (visaRequirement.notes != null) ...[
              const SizedBox(height: 16),
              _buildNotes(theme),
            ],
            const SizedBox(height: 20),
            _buildActionButton(theme, requirementType),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, VisaRequirementType requirementType) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: requirementType.colorithAlpha(26),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            requirementType.icon,
            color: requirementType.color,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                requirementType.displayName,
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: requirementType.color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${visaRequirement.countryFrom} → ${visaRequirement.countryTo}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceithAlpha(153),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDescription(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighestithAlpha(77),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        visaRequirement.description,
        style: theme.textTheme.bodyLarge?.copyWith(
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildDetails(ThemeData theme) {
    final details = <Widget>[];

    if (visaRequirement.maxStayDuration != null) {
      details.add(_buildDetailItem(
        theme,
        Icons.schedule,
        'Maximum Stay',
        visaRequirement.formattedMaxStayDuration!,
      ));
    }

    if (visaRequirement.processingTime != null) {
      details.add(_buildDetailItem(
        theme,
        Icons.timer,
        'Processing Time',
        visaRequirement.formattedProcessingTime!,
      ));
    }

    if (visaRequirement.visaFee != null) {
      details.add(_buildDetailItem(
        theme,
        Icons.attach_money,
        'Visa Fee',
        visaRequirement.formattedVisaFee!,
      ));
    }

    if (details.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Details',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...details,
      ],
    );
  }

  Widget _buildDetailItem(
    ThemeData theme,
    IconData icon,
    String label,
    String value,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceithAlpha(179),
              ),
            ),
          ),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocuments(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Required Documents',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color: theme.colorScheme.outlineithAlpha(77),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: visaRequirementequiredDocuments
                .map((document) => _buildDocumentItem(theme, document))
                .toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentItem(ThemeData theme, String document) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 16,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              document,
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotes(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amberithAlpha(26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.amberithAlpha(77),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.amber.shade700,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              visaRequirement.notes!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.amber.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
      ThemeData theme, VisaRequirementType requirementType) {
    String buttonText;
    IconData buttonIcon;
    Color? buttonColor;

    switch (requirementType) {
      case VisaRequirementType.noVisaRequired:
      case VisaRequirementType.visaExemption:
        buttonText = 'Travel Freely';
        buttonIcon = Icons.check_circle;
        buttonColor = Colors.green;
        break;
      case VisaRequirementType.visaRequired:
        buttonText = 'Find Visa Services';
        buttonIcon = Icons.business;
        buttonColor = null;
        break;
      case VisaRequirementType.eVisa:
        buttonText = 'Apply for e-Visa';
        buttonIcon = Icons.computer;
        buttonColor = Colors.blue;
        break;
      case VisaRequirementType.visaOnArrival:
        buttonText = 'Prepare for Arrival';
        buttonIcon = Icons.flight_land;
        buttonColor = Colors.orange;
        break;
      case VisaRequirementTypeecialPermit:
        buttonText = 'Get Special Permit';
        buttonIcon = Icons.assignment_late;
        buttonColor = Colors.purple;
        break;
    }

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onActionPressed,
        icon: Icon(buttonIcon),
        label: Text(
          buttonText,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          foregroundColor: buttonColor != null ? Colorshite : null,
          padding: EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
}
