import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A card widget for displaying a travel document
class DocumentCard extends StatelessWidget {
  /// The travel document to display
  final TravelDocument document;

  /// Whether to show the full details
  final bool showFullDetails;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Callback when the edit button is tapped
  final VoidCallback? onEdit;

  /// Callback when the delete button is tapped
  final VoidCallback? onDelete;

  /// Creates a new document card
  const DocumentCard({
    super.key,
    required this.document,
    this.showFullDetails = false,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                document.type.colorithAlpha(50),
                Colorshite,
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: document.type.colorithAlpha(30),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      document.type.icon,
                      color: document.type.color,
                      size: 24,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            document.type.displayName,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: document.type.color,
                            ),
                          ),
                          Text(
                            document.name,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                    _buildStatusBadge(),
                  ],
                ),
              ),

              // Content
              Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Document number
                    Row(
                      children: [
                        Icon(
                          Icons.numbers,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: 8),
                        Text(
                          'Document Number:',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(width: 4),
                        Text(
                          document.documentNumber,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 8),

                    // Issued by
                    Row(
                      children: [
                        Icon(
                          Icons.location_city,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: 8),
                        Text(
                          'Issued by:',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(width: 4),
                        Text(
                          document.issuedBy,
                          style: TextStyle(
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 8),

                    // Dates
                    Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                              SizedBox(width: 8),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Issued:',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  Text(
                                    document.formattedIssuedDate,
                                    style: TextStyle(
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Row(
                            children: [
                              Icon(
                                Icons.event,
                                size: 16,
                                color: document.isExpiringSoon
                                    ? Colors.orange
                                    : document.isExpired
                                        ? Colorsed
                                        : Colors.grey[600],
                              ),
                              SizedBox(width: 8),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Expires:',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  Text(
                                    document.formattedExpiryDate,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: document.isExpiringSoon
                                          ? Colors.orange
                                          : document.isExpired
                                              ? Colorsed
                                              : null,
                                      fontWeight: document.isExpiringSoon ||
                                              document.isExpired
                                          ? FontWeight.bold
                                          : null,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    // Additional details for specific document types
                    if (showFullDetails) ...[
                      SizedBox(height: 16),
                      _buildAdditionalDetails(),
                    ],
                  ],
                ),
              ),

              // Actions
              if (onEdit != null || onDelete != null)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (onEdit != null)
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: onEdit,
                          tooltip: 'Edit',
                          color: AppTheme.primaryColor,
                        ),
                      if (onDelete != null)
                        IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: onDelete,
                          tooltip: 'Delete',
                          color: Colorsed,
                        ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the status badge
  Widget _buildStatusBadge() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: document.status.colorithAlpha(50),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            document.status.icon,
            color: document.status.color,
            size: 12,
          ),
          SizedBox(width: 4),
          Text(
            document.status.displayName,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: document.status.color,
            ),
          ),
        ],
      ),
    );
  }

  /// Build additional details based on document type
  Widget _buildAdditionalDetails() {
    if (document is Passport) {
      final passport = document as Passport;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(),
          SizedBox(height: 8),
          Text(
            'Passport Details',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          _buildDetailRow('Nationality', passport.nationality),
          SizedBox(height: 4),
          _buildDetailRow('Country Code', passport.countryCode),
          SizedBox(height: 4),
          _buildDetailRow('Place of Birth', passport.placeOfBirth),
          SizedBox(height: 4),
          _buildDetailRow(
              'Date of Birth', passport.dateOfBirth.toString()lit(' ')[0]),
          SizedBox(height: 4),
          _buildDetailRow('Gender', passport.gender),
        ],
      );
    } else if (document is Visa) {
      final visa = document as Visa;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(),
          SizedBox(height: 8),
          Text(
            'Visa Details',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          _buildDetailRow('Visa Type', visa.visaType.displayName),
          SizedBox(height: 4),
          _buildDetailRow('Entry Type', visa.entryType.displayName),
          SizedBox(height: 4),
          _buildDetailRow('Country of Issue', visa.countryOfIssue),
          SizedBox(height: 4),
          _buildDetailRow('Valid For', visa.countryValidFor),
          SizedBox(height: 4),
          _buildDetailRow('Max Stay', '${visa.maxStayDuration} days'),
          if (visa.processingTime != null) ...[
            SizedBox(height: 4),
            _buildDetailRow('Processing Time', '${visa.processingTime} days'),
          ],
          if (visa.applicationReference != null) ...[
            SizedBox(height: 4),
            _buildDetailRow('Reference', visa.applicationReference!),
          ],
        ],
      );
    }

    return const SizedBox.shrink();
  }

  /// Build a detail row with label and value
  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            '$label:',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight500,
            ),
          ),
        ),
      ],
    );
  }
}
