import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/itinerary_item.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';

/// A widget for selecting preferences for an itinerary
class PreferencesStep extends ConsumerStatefulWidget {
  /// The initial item types
  final Map<ItineraryItemType, bool> initialItemTypes;

  /// The initial categories
  final List<String> initialCategories;

  /// Callback when the item types change
  final Function(Map<ItineraryItemType, bool>) onItemTypesChanged;

  /// Callback when the categories change
  final Function(List<String>) onCategoriesChanged;

  /// Creates a new preferences step
  const PreferencesStep({
    super.key,
    required this.initialItemTypes,
    required this.initialCategories,
    required this.onItemTypesChanged,
    required this.onCategoriesChanged,
  });

  @override
  ConsumerState<PreferencesStep> createState() => _PreferencesStepState();
}

class _PreferencesStepState extends ConsumerState<PreferencesStep> {
  late Map<ItineraryItemType, bool> _itemTypes;
  late List<String> _selectedCategories;

  final List<String> _availableCategories = [
    'Cultural',
    'Adventure',
    'Relaxation',
    'Food & Drink',
    'Shopping',
    'Nature',
    'History',
    'Art',
    'Music',
    'Nightlife',
    'Family-friendly',
    'Romantic',
    'Budget',
    'Luxury',
    'Off the beaten path',
  ];

  @override
  void initState() {
    super.initState();
    _itemTypes = Map.from(widget.initialItemTypes);
    _selectedCategories = List.from(widget.initialCategories);
  }

  /// Toggle an item type
  void _toggleItemType(ItineraryItemType type, bool value) {
    setState(() {
      _itemTypes[type] = value;
      widget.onItemTypesChanged(_itemTypes);
    });
  }

  /// Toggle a category
  void _toggleCategory(String category) {
    setState(() {
      if (_selectedCategories.contains(category)) {
        _selectedCategoriesemove(category);
      } else {
        _selectedCategories.add(category);
      }
      widget.onCategoriesChanged(_selectedCategories);
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'What are you interested in?',
            style: AppTextStyleseadline6,
          ),

          SizedBox(height: 8),

          // Subtitle
          Text(
            'Select your preferences to get personalized recommendations',
            style: AppTextStyles.subtitle2.copyWith(
              color: Colors.grey[600],
            ),
          ),

          SizedBox(height: 24),

          // Item types section
          Text(
            'What would you like to include in your itinerary?',
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          SizedBox(height: 16),

          // Item types grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 3,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: ItineraryItemType.values.length,
            itemBuilder: (context, index) {
              final type = ItineraryItemType.values[index];
              return _ItemTypeCard(
                type: type,
                isSelected: _itemTypes[type] ?? false,
                onToggle: (value) => _toggleItemType(type, value),
              );
            },
          ),

          SizedBox(height: 32),

          // Categories section
          Text(
            'What categories are you interested in?',
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          SizedBox(height: 8),

          Text(
            'Select all that apply',
            style: AppTextStyles.body2.copyWith(
              color: Colors.grey[600],
            ),
          ),

          SizedBox(height: 16),

          // Categories wrap
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _availableCategories.map((category) {
              final isSelected = _selectedCategories.contains(category);
              return FilterChip(
                label: Text(category),
                selected: isSelected,
                onSelected: (_) => _toggleCategory(category),
                backgroundColor: Colors.grey[200],
                selectedColor: AppColors.primaryithAlpha(51),
                checkmarkColor: AppColors.primary,
                labelStyle: TextStyle(
                  color: isSelected ? AppColors.primary : Colors.black,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color: isSelected ? AppColors.primary : Colors.transparent,
                  ),
                ),
              );
            }).toList(),
          ),

          SizedBox(height: 32),

          // Personalization note
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primaryithAlpha(25),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.primaryithAlpha(77)),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.lightbulb,
                  color: AppColors.primary,
                  size: 24,
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'AI-Powered Recommendations',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Our AI will use your preferences to suggest personalized activities, accommodations, and more for your trip.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// A card for selecting an item type
class _ItemTypeCard extends StatelessWidget {
  /// The item type
  final ItineraryItemType type;

  /// Whether the item type is selected
  final bool isSelected;

  /// Callback when the item type is toggled
  final Function(bool) onToggle;

  /// Creates a new item type card
  const _ItemTypeCard({
    required this.type,
    required this.isSelected,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 2 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: isSelected ? type.color : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () => onToggle(!isSelected),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              Icon(
                type.icon,
                color: isSelected ? type.color : Colors.grey,
                size: 24,
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  type.displayName,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? type.color : Colors.black,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Checkbox(
                value: isSelected,
                onChanged: (value) => onToggle(value ?? false),
                activeColor: type.color,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
