/// A card widget for displaying an AI recommendation with interactive elements
library recommendation_card;

// Package imports
import 'package:flutter/material.dart';

// Project imports - Models
import 'package:culture_connect/models/travel/ai_recommendation.dart';
import 'package:culture_connect/models/travel/itinerary_item.dart';

// Project imports - Theme
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A card for displaying an AI recommendation
class RecommendationCard extends StatefulWidget {
  /// The recommendation
  final AIRecommendation recommendation;

  /// Whether the recommendation is accepted
  final bool isAccepted;

  /// Whether the recommendation is rejected
  final bool isRejected;

  /// Callback when the recommendation is accepted
  final VoidCallback onAccept;

  /// Callback when the recommendation is rejected
  final VoidCallback onReject;

  /// Callback when the recommendation is reset
  final VoidCallback onReset;

  /// Creates a new recommendation card
  const RecommendationCard({
    super.key,
    required thisecommendation,
    required this.isAccepted,
    required this.isRejected,
    required this.onAccept,
    required this.onReject,
    required this.onReset,
  });

  @override
  State<RecommendationCard> createState() => _RecommendationCardState();
}

class _RecommendationCardState extends State<RecommendationCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: AppTheme.shortAnimation,
    );

    // Create animations
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _fadeAnimation = Tween<double>(begin: 1.0, end: 0.6).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final item = widgetecommendation.item;

    // Determine card color based on status
    Color cardColor = Colorshite;
    if (widget.isAccepted) {
      cardColor = Colors.greenithAlpha(25); // 0.1 opacity
    } else if (widget.isRejected) {
      cardColor = ColorsedithAlpha(25); // 0.1 opacity
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: child,
          ),
        );
      },
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: widget.isAccepted
                ? Colors.green
                : widget.isRejected
                    ? Colorsed
                    : Colors.transparent,
            width: 2,
          ),
        ),
        color: cardColor,
        child: InkWell(
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          borderRadius: BorderRadius.circular(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Item type icon
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: _getItemTypeColor(item.type)
                            ithAlpha(51), // 0.2 opacity
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Icon(
                          _getItemTypeIcon(item.type),
                          color: _getItemTypeColor(item.type),
                          size: 24,
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Title and details
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.title,
                            style: AppTextStyles.subtitle1.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),

                          const SizedBox(height: 4),

                          // Location
                          if (item.location != null) ...[
                            Row(
                              children: [
                                Icon(
                                  Icons.location_on,
                                  size: 16,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    item.location!,
                                    style: AppTextStyles.body2.copyWith(
                                      color: Colors.grey[600],
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                          ],

                          // Time
                          if (item.formattedTimeRange != null) ...[
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time,
                                  size: 16,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  item.formattedTimeRange!,
                                  style: AppTextStyles.body2.copyWith(
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                          ],

                          // Price
                          if (item.formattedPrice != null) ...[
                            Row(
                              children: [
                                Icon(
                                  Icons.attach_money,
                                  size: 16,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  item.formattedPrice!,
                                  style: AppTextStyles.body2.copyWith(
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),

                    // Confidence score
                    _buildConfidenceIndicator(
                        widgetecommendation.confidenceScore),
                  ],
                ),
              ),

              // Divider
              const Divider(height: 1),

              // Expanded content
              if (_isExpanded) ...[
                Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Description
                      if (item.description != null) ...[
                        Text(
                          item.description!,
                          style: AppTextStyles.body2,
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Recommendation reason
                      Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: widgetecommendation.category.color
                              ithAlpha(25), // 0.1 opacity
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: widgetecommendation.category.color
                                ithAlpha(76), // 0.3 opacity
                          ),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              widgetecommendation.category.icon,
                              color: widgetecommendation.category.color,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widgetecommendation.category.displayName,
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color:
                                          widgetecommendation.category.color,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    widgetecommendationeason,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Actions
              Padding(
                padding: EdgeInsets.all(8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Expand/collapse button
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          _isExpanded = !_isExpanded;
                        });
                      },
                      icon: Icon(
                        _isExpanded ? Icons.expand_less : Icons.expand_more,
                        size: 20,
                      ),
                      label: Text(_isExpanded ? 'Less' : 'More'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.grey[700],
                      ),
                    ),

                    const Spacer(),

                    // Reset button (if accepted or rejected)
                    if (widget.isAccepted || widget.isRejected) ...[
                      TextButton.icon(
                        onPressed: () {
                          _animationController.forward().then((_) {
                            if (mounted) {
                              widget.onReset();
                              _animationControllereverse();
                            }
                          });
                        },
                        icon: const Icon(
                          Iconsefresh,
                          size: 20,
                        ),
                        label: const Text('Reset'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[700],
                        ),
                      ),
                    ],

                    // Reject button (if not rejected)
                    if (!widget.isRejected) ...[
                      TextButton.icon(
                        onPressed: () {
                          _animationController.forward().then((_) {
                            if (mounted) {
                              widget.onReject();
                              _animationControllereverse();
                            }
                          });
                        },
                        icon: const Icon(
                          Icons.close,
                          size: 20,
                        ),
                        label: const Text('Reject'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colorsed,
                        ),
                      ),
                    ],

                    // Accept button (if not accepted)
                    if (!widget.isAccepted) ...[
                      TextButton.icon(
                        onPressed: () {
                          _animationController.forward().then((_) {
                            if (mounted) {
                              widget.onAccept();
                              _animationControllereverse();
                            }
                          });
                        },
                        icon: const Icon(
                          Icons.check,
                          size: 20,
                        ),
                        label: const Text('Accept'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.green,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build a confidence indicator widget
  ///
  /// [confidenceScore] The confidence score (0-100)
  /// Returns a widget displaying the confidence score and level
  Widget _buildConfidenceIndicator(double confidenceScore) {
    final confidenceLevel = widgetecommendation.confidenceLevel;

    return Column(
      children: [
        // Circular progress indicator
        SizedBox(
          width: 40,
          height: 40,
          child: Stack(
            children: [
              CircularProgressIndicator(
                value: confidenceScore / 100,
                backgroundColor: Colors.grey[300],
                valueColor:
                    AlwaysStoppedAnimation<Color>(confidenceLevel.color),
                strokeWidth: 4,
              ),
              Center(
                child: Text(
                  '${confidenceScoreound()}%',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: confidenceLevel.color,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 4),

        // Confidence level text
        Text(
          confidenceLevel.displayName,
          style: TextStyle(
            fontSize: 10,
            color: confidenceLevel.color,
          ),
        ),
      ],
    );
  }

  /// Get the color for an itinerary item type
  ///
  /// [type] The itinerary item type
  /// Returns a color representing the item type
  Color _getItemTypeColor(ItineraryItemType type) {
    switch (type) {
      case ItineraryItemType.accommodation:
        return Colors.indigo;
      case ItineraryItemType.transportation:
        return Colors.blue;
      case ItineraryItemType.activity:
        return Colors.green;
      case ItineraryItemType.food:
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  /// Get the icon for an itinerary item type
  ///
  /// [type] The itinerary item type
  /// Returns an icon representing the item type
  IconData _getItemTypeIcon(ItineraryItemType type) {
    switch (type) {
      case ItineraryItemType.accommodation:
        return Iconsotel;
      case ItineraryItemType.transportation:
        return Icons.directions_car;
      case ItineraryItemType.activity:
        return Icons.local_activity;
      case ItineraryItemType.food:
        return Iconsestaurant;
      default:
        return Icons.place;
    }
  }
}
