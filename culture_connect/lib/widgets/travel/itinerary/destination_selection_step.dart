import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';

/// A widget for selecting a destination for an itinerary
class DestinationSelectionStep extends ConsumerStatefulWidget {
  /// The initial destination
  final String initialDestination;

  /// Callback when the destination changes
  final Function(String) onDestinationChanged;

  /// Creates a new destination selection step
  const DestinationSelectionStep({
    super.key,
    this.initialDestination = '',
    required this.onDestinationChanged,
  });

  @override
  ConsumerState<DestinationSelectionStep> createState() =>
      _DestinationSelectionStepState();
}

class _DestinationSelectionStepState
    extends ConsumerState<DestinationSelectionStep> {
  late TextEditingController _destinationController;
  final FocusNode _destinationFocusNode = FocusNode();

  final List<String> _popularDestinations = [
    'Paris, France',
    'Tokyo, Japan',
    'New York, USA',
    'Rome, Italy',
    'Sydney, Australia',
    'Cairo, Egypt',
    'Rio de Janeiro, Brazil',
    'Cape Town, South Africa',
    'Bangkok, Thailand',
    'Dubai, UAE',
    'London, UK',
    'Barcelona, Spain',
  ];

  final List<String> _recentDestinations = [
    'Lagos, Nigeria',
    'Accra, Ghana',
    'Nairobi, Kenya',
    'Marrakech, Morocco',
    'Zanzibar, Tanzania',
  ];

  @override
  void initState() {
    super.initState();
    _destinationController =
        TextEditingController(text: widget.initialDestination);

    // Focus the destination field when the widget is first built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.initialDestination.isEmpty) {
        _destinationFocusNodeequestFocus();
      }
    });
  }

  @override
  void dispose() {
    _destinationController.dispose();
    _destinationFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Where would you like to go?',
            style: AppTextStyleseadline6,
          ),

          SizedBox(height: 8),

          // Subtitle
          Text(
            'Enter a destination for your trip',
            style: AppTextStyles.subtitle2.copyWith(
              color: Colors.grey[600],
            ),
          ),

          SizedBox(height: 24),

          // Destination input
          TextField(
            controller: _destinationController,
            focusNode: _destinationFocusNode,
            decoration: InputDecoration(
              labelText: 'Destination',
              hintText: 'e.g. Paris, France',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _destinationController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _destinationController.clear();
                        widget.onDestinationChanged('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: widget.onDestinationChanged,
            textInputAction: TextInputAction.done,
          ),

          SizedBox(height: 32),

          // Popular destinations
          Text(
            'Popular Destinations',
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          SizedBox(height: 16),

          // Popular destinations grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 2.5,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: _popularDestinations.length,
            itemBuilder: (context, index) {
              return _DestinationCard(
                destination: _popularDestinations[index],
                onTap: () {
                  _destinationController.text = _popularDestinations[index];
                  widget.onDestinationChanged(_popularDestinations[index]);
                },
                isSelected:
                    _destinationController.text == _popularDestinations[index],
              );
            },
          ),

          SizedBox(height: 32),

          // Recent destinations
          Text(
            'Recent Destinations',
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          SizedBox(height: 16),

          // Recent destinations list
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _recentDestinations.length,
            itemBuilder: (context, index) {
              return ListTile(
                leading: const Icon(Iconsistory),
                title: Text(_recentDestinations[index]),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  _destinationController.text = _recentDestinations[index];
                  widget.onDestinationChanged(_recentDestinations[index]);
                },
                selected:
                    _destinationController.text == _recentDestinations[index],
                selectedTileColor: AppColors.primaryithAlpha(25),
              );
            },
          ),
        ],
      ),
    );
  }
}

/// A card for displaying a destination
class _DestinationCard extends StatelessWidget {
  /// The destination
  final String destination;

  /// Callback when the card is tapped
  final VoidCallback onTap;

  /// Whether the card is selected
  final bool isSelected;

  /// Creates a new destination card
  const _DestinationCard({
    required this.destination,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: isSelected ? AppColors.primary : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: EdgeInsets.all(12),
          child: Center(
            child: Text(
              destination,
              style: TextStyle(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? AppColors.primary : null,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }
}
