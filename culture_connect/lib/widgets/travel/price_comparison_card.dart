import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/price_comparison_model.dart';

import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/animated_price_tag.dart';
import 'package:url_launcher/url_launcher.dart';

/// A widget that displays a price comparison card
class PriceComparisonCard extends ConsumerWidget {
  /// The price point to display
  final PricePoint pricePoint;

  /// Whether this is the best price
  final bool isBestPrice;

  /// Whether to show the booking button
  final bool showBookingButton;

  /// Whether to show the price breakdown
  final bool showPriceBreakdown;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Creates a new price comparison card
  const PriceComparisonCard({
    super.key,
    required this.pricePoint,
    this.isBestPrice = false,
    this.showBookingButton = true,
    this.showPriceBreakdown = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: isBestPrice
            ? BorderSide(color: AppTheme.primaryColor, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Source and price
              Row(
                children: [
                  // Source logo and name
                  Expanded(
                    child: Row(
                      children: [
                        // Logo placeholder (in a real app, this would be an image)
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Text(
                              pricePoint.source.name.substring(0, 1),
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[800],
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 12),
                        // Source name and rating
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                pricePoint.source.name,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: 4),
                              Row(
                                children: [
                                  Icon(
                                    Icons.star,
                                    size: 16,
                                    color: Colors.amber,
                                  ),
                                  SizedBox(width: 4),
                                  Text(
                                    '${pricePoint.sourceating} (${pricePoint.sourceeviewCount})',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: AppTheme.textSecondaryColor,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Price
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      AnimatedPriceTag(
                        price: pricePoint.finalPrice,
                        currency: pricePoint.currency,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: isBestPrice
                              ? AppTheme.primaryColor
                              : AppTheme.textPrimaryColor,
                        ),
                      ),
                      if (pricePoint.discountAmount > 0) ...[
                        SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              pricePoint.formattedBasePrice,
                              style: TextStyle(
                                fontSize: 12,
                                color: AppTheme.textSecondaryColor,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                            SizedBox(width: 4),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.green[100],
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                '-${(pricePoint.discountAmount / pricePoint.basePrice * 100).toStringAsFixed(0)}%',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green[700],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ],
              ),

              // Best price badge
              if (isBestPrice) ...[
                SizedBox(height: 12),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColorithAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.primaryColor,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.check_circle,
                        size: 16,
                        color: AppTheme.primaryColor,
                      ),
                      SizedBox(width: 4),
                      Text(
                        'Best Price',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Price breakdown
              if (showPriceBreakdown) ...[
                SizedBox(height: 16),
                Text(
                  'Price Breakdown',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                _buildPriceBreakdownItem(
                  'Base Price',
                  pricePoint.formattedBasePrice,
                ),
                _buildPriceBreakdownItem(
                  'Taxes',
                  pricePoint.formattedTaxAmount,
                ),
                _buildPriceBreakdownItem(
                  'Fees',
                  pricePoint.formattedFeeAmount,
                ),
                if (pricePoint.discountAmount > 0)
                  _buildPriceBreakdownItem(
                    'Discount',
                    '-${pricePoint.formattedDiscountAmount}',
                    isDiscount: true,
                  ),
                Divider(height: 16),
                _buildPriceBreakdownItem(
                  'Total',
                  pricePoint.formattedFinalPrice,
                  isTotal: true,
                ),
              ],

              // Additional details
              SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  if (pricePoint.details.containsKey('cancellationPolicy'))
                    _buildDetailChip(
                      Icons.event_busy,
                      pricePoint.details['cancellationPolicy'] as String,
                    ),
                  if (pricePoint.details.containsKey('availability'))
                    _buildDetailChip(
                      Icons.event_available,
                      'Only ${pricePoint.details['availability']} left',
                    ),
                ],
              ),

              // Booking button
              if (showBookingButton) ...[
                SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => _launchBookingUrl(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colorshite,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Book on ${pricePoint.source.name}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],

              // Cache indicator
              if (pricePoint.isFromCache) ...[
                SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                    SizedBox(width: 4),
                    Text(
                      'Price from ${_formatTimestamp(pricePoint.timestamp)}',
                      style: TextStyle(
                        fontSize: 10,
                        color: AppTheme.textSecondaryColor,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriceBreakdownItem(String label, String value,
      {bool isDiscount = false, bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignmentaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color:
                  isDiscount ? Colors.green[700] : AppTheme.textSecondaryColor,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isDiscount ? Colors.green[700] : AppTheme.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailChip(IconData icon, String label) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else {
      return DateFormat('MMM d, h:mm a').format(timestamp);
    }
  }

  Future<void> _launchBookingUrl(BuildContext context) async {
    final url = Uri.parse(pricePoint.bookingUrl);

    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open ${pricePoint.source.name}'),
            backgroundColor: Colorsed,
          ),
        );
      }
    }
  }
}
