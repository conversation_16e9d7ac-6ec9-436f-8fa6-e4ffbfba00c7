import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:culture_connect/services/payment_api_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Widget for viewing and sharing payment receipts
class PaymentReceiptViewer extends StatefulWidget {
  final String receiptId;
  final String transactionReference;
  final PaymentApiService paymentApiService;
  final LoggingService loggingService;

  const PaymentReceiptViewer({
    super.key,
    required this.receiptId,
    required this.transactionReference,
    required this.paymentApiService,
    required this.loggingService,
  });

  @override
  State<PaymentReceiptViewer> createState() => _PaymentReceiptViewerState();
}

class _PaymentReceiptViewerState extends State<PaymentReceiptViewer> {
  bool _isLoading = true;
  bool _isSharing = false;
  String? _errorMessage;
  String? _pdfPath;
  int _totalPages = 0;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _loadReceipt();
  }

  /// Load receipt PDF from backend
  /// 
  /// TODO: Backend Integration Required
  /// - Call GET /api/payments/receipt/{receiptId}
  /// - Handle PDF download and caching
  /// - Implement offline receipt storage
  Future<void> _loadReceipt() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      widget.loggingService.info(
        'PaymentReceiptViewer',
        'Loading receipt PDF',
        {
          'receiptId': widget.receiptId,
          'transactionReference': widget.transactionReference,
        },
      );

      // Download receipt PDF from backend
      final pdfBytes = await widget.paymentApiService.downloadReceipt(widget.receiptId);

      // Save PDF to temporary directory
      final tempDir = await getTemporaryDirectory();
      final pdfFile = File('${tempDir.path}/receipt_${widget.receiptId}.pdf');
      await pdfFile.writeAsBytes(pdfBytes);

      if (mounted) {
        setState(() {
          _pdfPath = pdfFile.path;
          _isLoading = false;
        });

        widget.loggingService.info(
          'PaymentReceiptViewer',
          'Receipt PDF loaded successfully',
          {
            'receiptId': widget.receiptId,
            'fileSize': pdfBytes.length,
          },
        );
      }

    } catch (e, stackTrace) {
      widget.loggingService.error(
        'PaymentReceiptViewer',
        'Failed to load receipt PDF',
        {
          'receiptId': widget.receiptId,
          'error': e.toString(),
        },
        stackTrace,
      );

      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load receipt: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// Share receipt PDF
  /// 
  /// TODO: Backend Integration Required
  /// - Generate shareable receipt link
  /// - Support multiple sharing options (email, messaging, social)
  /// - Track sharing analytics
  Future<void> _shareReceipt() async {
    if (_pdfPath == null || _isSharing) return;

    try {
      setState(() {
        _isSharing = true;
      });

      HapticFeedback.lightImpact();

      widget.loggingService.info(
        'PaymentReceiptViewer',
        'Sharing receipt',
        {
          'receiptId': widget.receiptId,
          'transactionReference': widget.transactionReference,
        },
      );

      // Share PDF file
      await Share.shareXFiles(
        [XFile(_pdfPath!)],
        text: 'Payment Receipt - Transaction ${widget.transactionReference}',
        subject: 'CultureConnect Payment Receipt',
      );

    } catch (e, stackTrace) {
      widget.loggingService.error(
        'PaymentReceiptViewer',
        'Failed to share receipt',
        {
          'receiptId': widget.receiptId,
          'error': e.toString(),
        },
        stackTrace,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share receipt: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSharing = false;
        });
      }
    }
  }

  /// Download receipt to device storage
  /// 
  /// TODO: Backend Integration Required
  /// - Save to user's downloads folder
  /// - Handle storage permissions
  /// - Show download completion notification
  Future<void> _downloadReceipt() async {
    if (_pdfPath == null) return;

    try {
      HapticFeedback.lightImpact();

      // Get downloads directory
      final downloadsDir = await getDownloadsDirectory();
      if (downloadsDir == null) {
        throw Exception('Downloads directory not available');
      }

      // Copy PDF to downloads
      final sourceFile = File(_pdfPath!);
      final targetFile = File('${downloadsDir.path}/CultureConnect_Receipt_${widget.transactionReference}.pdf');
      await sourceFile.copy(targetFile.path);

      widget.loggingService.info(
        'PaymentReceiptViewer',
        'Receipt downloaded successfully',
        {
          'receiptId': widget.receiptId,
          'downloadPath': targetFile.path,
        },
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Receipt downloaded successfully'),
            backgroundColor: AppTheme.successColor,
            action: SnackBarAction(
              label: 'Open',
              textColor: Colors.white,
              onPressed: () {
                // TODO: Open file with system default app
              },
            ),
          ),
        );
      }

    } catch (e, stackTrace) {
      widget.loggingService.error(
        'PaymentReceiptViewer',
        'Failed to download receipt',
        {
          'receiptId': widget.receiptId,
          'error': e.toString(),
        },
        stackTrace,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to download receipt: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: const Text('Payment Receipt'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          if (_pdfPath != null) ...[
            IconButton(
              icon: const Icon(Icons.download),
              onPressed: _downloadReceipt,
              tooltip: 'Download',
            ),
            IconButton(
              icon: _isSharing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.share),
              onPressed: _shareReceipt,
              tooltip: 'Share',
            ),
          ],
        ],
      ),
      body: _buildBody(theme),
      bottomNavigationBar: _pdfPath != null ? _buildBottomBar(theme) : null,
    );
  }

  /// Build main body content
  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return _buildLoadingState(theme);
    } else if (_errorMessage != null) {
      return _buildErrorState(theme);
    } else if (_pdfPath != null) {
      return _buildPdfViewer(theme);
    } else {
      return _buildEmptyState(theme);
    }
  }

  /// Build loading state
  Widget _buildLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Loading receipt...',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            'Transaction: ${widget.transactionReference}',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(128),
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              'Failed to Load Receipt',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              _errorMessage!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(179),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingLarge),
            FilledButton(
              onPressed: _loadReceipt,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64,
            color: theme.colorScheme.onSurface.withAlpha(128),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'No Receipt Available',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build PDF viewer
  Widget _buildPdfViewer(ThemeData theme) {
    return PDFView(
      filePath: _pdfPath!,
      enableSwipe: true,
      swipeHorizontal: false,
      autoSpacing: false,
      pageFling: true,
      pageSnap: true,
      defaultPage: _currentPage,
      fitPolicy: FitPolicy.BOTH,
      preventLinkNavigation: false,
      onRender: (pages) {
        setState(() {
          _totalPages = pages ?? 0;
        });
      },
      onViewCreated: (PDFViewController controller) {
        // PDF controller can be used for additional functionality
      },
      onLinkHandler: (String? uri) {
        // Handle PDF links if needed
      },
      onError: (error) {
        widget.loggingService.error(
          'PaymentReceiptViewer',
          'PDF viewer error',
          {'error': error.toString()},
        );
      },
      onPageError: (page, error) {
        widget.loggingService.error(
          'PaymentReceiptViewer',
          'PDF page error',
          {'page': page, 'error': error.toString()},
        );
      },
      onPageChanged: (int? page, int? total) {
        setState(() {
          _currentPage = page ?? 0;
        });
      },
    );
  }

  /// Build bottom navigation bar
  Widget _buildBottomBar(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withAlpha(51),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Page ${_currentPage + 1} of $_totalPages',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
          ),
          Row(
            children: [
              OutlinedButton.icon(
                onPressed: _downloadReceipt,
                icon: const Icon(Icons.download, size: 18),
                label: const Text('Download'),
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              FilledButton.icon(
                onPressed: _isSharing ? null : _shareReceipt,
                icon: _isSharing
                    ? const SizedBox(
                        width: 18,
                        height: 18,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.share, size: 18),
                label: const Text('Share'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
