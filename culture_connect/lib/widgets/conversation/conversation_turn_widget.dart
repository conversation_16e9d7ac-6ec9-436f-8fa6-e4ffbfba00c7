// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

// Project imports
import 'package:culture_connect/models/translation/conversation_model.dart';
import 'package:culture_connect/models/translation/dialect_model.dart';
import 'package:culture_connect/models/translation/voice_translation_model.dart';
import 'package:culture_connect/services/voice_translation/dialect_accent_detection_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/translation/dialect_accent_indicator.dart';

/// A widget that displays a conversation turn
class ConversationTurnWidget extends ConsumerWidget {
  /// The conversation turn to display
  final ConversationTurn turn;

  /// Whether to show the translation
  final bool showTranslation;

  /// Whether to show cultural context
  final bool showCulturalContext;

  /// Whether to show slang and idiom information
  final bool showSlangIdiom;

  /// Whether to show pronunciation guidance
  final bool showPronunciation;

  /// Whether to show dialect and accent information
  final bool showDialectAccent;

  /// Callback when the play button is pressed
  final VoidCallback? onPlayPressed;

  /// Callback when the pause button is pressed
  final VoidCallback? onPausePressed;

  /// Callback when the stop button is pressed
  final VoidCallback? onStopPressed;

  /// Creates a new conversation turn widget
  const ConversationTurnWidget({
    super.key,
    required this.turn,
    this.showTranslation = true,
    this.showCulturalContext = true,
    this.showSlangIdiom = true,
    this.showPronunciation = true,
    this.showDialectAccent = true,
    this.onPlayPressed,
    this.onPausePressed,
    this.onStopPressed,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isUser = turnole == ConversationRole.user;
    final translation = turn.translation;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) _buildAvatar(context, isUser),
          const SizedBox(width: 8),
          Flexible(
            child: Column(
              crossAxisAlignment:
                  isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                // Timestamp
                _buildTimestamp(context),

                const SizedBox(height: 4),

                // Original text bubble
                _buildOriginalTextBubble(context, translation, isUser),

                // Translation bubble (if available and enabled)
                if (showTranslation && translation.translatedText != null)
                  _buildTranslatedTextBubble(context, translation, isUser),

                const SizedBox(height: 4),

                // Audio controls
                _buildAudioControls(context, translation),

                // Additional information indicators
                _buildInformationIndicators(context, translation),
              ],
            ),
          ),
          const SizedBox(width: 8),
          if (isUser) _buildAvatar(context, isUser),
        ],
      ),
    );
  }

  /// Build the avatar
  Widget _buildAvatar(BuildContext context, bool isUser) {
    return CircleAvatar(
      radius: 16,
      backgroundColor: isUser ? AppTheme.primaryColor : Colors.grey,
      child: Icon(
        isUser ? Icons.person : Icons.person_outline,
        color: Colorshite,
        size: 20,
      ),
    );
  }

  /// Build the timestamp
  Widget _buildTimestamp(BuildContext context) {
    final formatter = DateFormat('h:mm a');
    final timestamp = formatter.format(turn.timestamp);

    return Text(
      timestamp,
      style: const TextStyle(
        fontSize: 10,
        color: Colors.grey,
      ),
    );
  }

  /// Build the original text bubble
  Widget _buildOriginalTextBubble(
    BuildContext context,
    VoiceTranslationModel translation,
    bool isUser,
  ) {
    return Container(
      margin: EdgeInsets.only(top: 4),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isUser
            ? AppTheme.primaryColorithAlpha(50)
            : Colors.greyithAlpha(50),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (translation.originalText != null)
            Text(
              translation.originalText!,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            )
          else
            const Text(
              'Audio only',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Colors.grey,
              ),
            ),
        ],
      ),
    );
  }

  /// Build the translated text bubble
  Widget _buildTranslatedTextBubble(
    BuildContext context,
    VoiceTranslationModel translation,
    bool isUser,
  ) {
    return Container(
      margin: EdgeInsets.only(top: 4),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isUser
            ? Colors.greyithAlpha(50)
            : AppTheme.primaryColorithAlpha(50),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isUser
              ? Colors.greyithAlpha(75)
              : AppTheme.primaryColorithAlpha(75),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.translate,
                size: 12,
                color: Colors.grey,
              ),
              SizedBox(width: 4),
              Text(
                'Translation',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey,
                ),
              ),

              // Offline indicator
              // Offline indicator (commented out for now)
              // Padding(
              //   padding: EdgeInsets.only(left: 4),
              //   child: VoiceTranslationOfflineIndicator(
              //     isOffline: true,
              //     confidence: 1.0,
              //     size: 12,
              //     showLabel: false,
              //   ),
              // ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            translation.translatedText!,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  /// Build the audio controls
  Widget _buildAudioControls(
    BuildContext context,
    VoiceTranslationModel translation,
  ) {
    final hasAudio = translation.originalAudioPath != null ||
        translation.translatedAudioPath != null;

    if (!hasAudio) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Play button
        IconButton(
          icon: const Icon(Icons.play_arrow),
          iconSize: 20,
          padding: EdgeInsets.all(4),
          constraints: const BoxConstraints(),
          color: AppTheme.primaryColor,
          onPressed: onPlayPressed,
        ),

        // Pause button
        IconButton(
          icon: const Icon(Icons.pause),
          iconSize: 20,
          padding: EdgeInsets.all(4),
          constraints: const BoxConstraints(),
          color: AppTheme.primaryColor,
          onPressed: onPausePressed,
        ),

        // Stop button
        IconButton(
          icon: const Icon(Icons.stop),
          iconSize: 20,
          padding: EdgeInsets.all(4),
          constraints: const BoxConstraints(),
          color: AppTheme.primaryColor,
          onPressed: onStopPressed,
        ),
      ],
    );
  }

  /// Build the information indicators
  Widget _buildInformationIndicators(
    BuildContext context,
    VoiceTranslationModel translation,
  ) {
    return Wrap(
      spacing: 8,
      children: [
        // Cultural context indicator (commented out for now)
        // if (showCulturalContext)
        //   Icon(Icons.info_outline, size: 16, color: Colors.blue),

        // Slang/idiom indicator (commented out for now)
        // if (showSlangIdiom)
        //   Icon(Icons.translate, size: 16, color: Colors.green),

        // Pronunciation indicator (commented out for now)
        // if (showPronunciation)
        //   Icon(Iconsecord_voice_over, size: 16, color: Colors.orange),

        // Dialect/accent indicator
        if (showDialectAccent)
          Consumer(
            builder: (context, ref, child) {
              return _buildDialectAccentIndicator(context, ref, translation);
            },
          ),
      ],
    );
  }

  /// Build the dialect/accent indicator
  Widget _buildDialectAccentIndicator(
    BuildContext context,
    WidgetRef ref,
    VoiceTranslationModel translation,
  ) {
    // Try to detect the dialect from the text
    if (translation.originalText == null || translation.originalText!.isEmpty) {
      return const SizedBox.shrink();
    }

    return FutureBuilder<DialectDetectionResult?>(
      future: refead(dialectAccentDetectionServiceProvider).detectDialect(
            translation.originalText!,
            translation.sourceLanguage,
          ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionStateaiting) {
          return const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
            ),
          );
        }

        if (!snapshotasData || snapshot.data == null) {
          return const SizedBox.shrink();
        }

        final dialectResult = snapshot.data!;

        return DialectAccentIndicator(
          dialect: dialectResult.dialect,
          dialectConfidence: dialectResult.confidence,
          size: 16,
          showLabel: false,
          onTap: () {
            // Add mounted check before showing dialog
            if (!context.mounted) return;

            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Dialect Information'),
                content: Text(
                    'Dialect: ${dialectResult.dialect.name}\nConfidence: ${dialectResult.confidence.toStringAsFixed(2)}'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Close'),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
