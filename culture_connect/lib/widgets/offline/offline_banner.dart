import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/providers/offline_mode_provider.dart';

/// A banner that displays when the device is offline
class OfflineBanner extends ConsumerStatefulWidget {
  /// The message to display
  final String message;

  /// The action text
  final String actionText;

  /// The callback when the action button is pressed
  final VoidCallback? onAction;

  /// Whether the banner is dismissible
  final bool isDismissible;

  /// The color of the banner
  final Color backgroundColor;

  /// The color of the text
  final Color textColor;

  /// The color of the action button
  final Color actionColor;

  /// Creates a new offline banner
  const OfflineBanner({
    super.key,
    this.message = 'You are offline. Some features may be limited.',
    this.actionText = 'RETRY',
    this.onAction,
    this.isDismissible = true,
    this.backgroundColor = Colors.orange,
    this.textColor = Colors.white,
    this.actionColor = Colors.white,
  });

  @override
  ConsumerState<OfflineBanner> createState() => _OfflineBannerState();
}

class _OfflineBannerState extends ConsumerState<OfflineBanner>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _heightAnimation;

  bool _isDismissed = false;

  @override
  void initState() {
    super.initState();

    // Create the animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Create the height animation
    _heightAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // Start the animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get the connectivity status
    final isOnline = ref.watch(isOnlineProvider);

    // Don't show the banner if online or dismissed
    if (isOnline || _isDismissed) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _heightAnimation,
      builder: (context, child) {
        return ClipRect(
          child: Align(
            heightFactor: _heightAnimation.value,
            child: child,
          ),
        );
      },
      child: Container(
        width: double.infinity,
        color: widget.backgroundColor,
        padding: EdgeInsets.symmetric(
          vertical: 8.h,
          horizontal: 16.w,
        ),
        child: Row(
          children: [
            // Offline icon
            Icon(
              Icons.wifi_off,
              color: widget.textColor,
              size: 20.r,
            ),

            SizedBox(width: 8.w),

            // Message
            Expanded(
              child: Text(
                widget.message,
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: 14.sp,
                ),
              ),
            ),

            // Action button
            if (widget.onAction != null)
              TextButton(
                onPressed: widget.onAction,
                style: TextButton.styleFrom(
                  foregroundColor: widget.actionColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: 8.w,
                    vertical: 4.h,
                  ),
                ),
                child: Text(
                  widget.actionText,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14.sp,
                  ),
                ),
              ),

            // Dismiss button
            if (widget.isDismissible)
              IconButton(
                icon: Icon(
                  Icons.close,
                  color: widget.textColor,
                  size: 20.r,
                ),
                onPressed: _dismiss,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
          ],
        ),
      ),
    );
  }

  /// Dismiss the banner
  void _dismiss() {
    _animationController.reverse().then((_) {
      setState(() {
        _isDismissed = true;
      });
    });
  }
}

/// A banner that displays when the device is offline with a custom appearance
class CustomOfflineBanner extends ConsumerStatefulWidget {
  /// The child widget to display when online
  final Widget child;

  /// The message to display when offline
  final String message;

  /// The action text
  final String actionText;

  /// The callback when the action button is pressed
  final VoidCallback? onAction;

  /// Whether the banner is dismissible
  final bool isDismissible;

  /// The color of the banner
  final Color backgroundColor;

  /// The color of the text
  final Color textColor;

  /// The color of the action button
  final Color actionColor;

  /// Creates a new custom offline banner
  const CustomOfflineBanner({
    super.key,
    required this.child,
    this.message = 'You are offline. Some features may be limited.',
    this.actionText = 'RETRY',
    this.onAction,
    this.isDismissible = true,
    this.backgroundColor = Colors.orange,
    this.textColor = Colors.white,
    this.actionColor = Colors.white,
  });

  @override
  ConsumerState<CustomOfflineBanner> createState() =>
      _CustomOfflineBannerState();
}

class _CustomOfflineBannerState extends ConsumerState<CustomOfflineBanner> {
  bool _isDismissed = false;

  @override
  Widget build(BuildContext context) {
    // Get the connectivity status
    final isOnline = ref.watch(isOnlineProvider);

    // If online or dismissed, just show the child
    if (isOnline || _isDismissed) {
      return widget.child;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Offline banner
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            vertical: 8.h,
            horizontal: 16.w,
          ),
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(25),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Offline icon
              Icon(
                Icons.wifi_off,
                color: widget.textColor,
                size: 20.r,
              ),

              SizedBox(width: 8.w),

              // Message
              Expanded(
                child: Text(
                  widget.message,
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: 14.sp,
                  ),
                ),
              ),

              // Action button
              if (widget.onAction != null)
                TextButton(
                  onPressed: widget.onAction,
                  style: TextButton.styleFrom(
                    foregroundColor: widget.actionColor,
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                  ),
                  child: Text(
                    widget.actionText,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                    ),
                  ),
                ),

              // Dismiss button
              if (widget.isDismissible)
                IconButton(
                  icon: Icon(
                    Icons.close,
                    color: widget.textColor,
                    size: 20.r,
                  ),
                  onPressed: () {
                    setState(() {
                      _isDismissed = true;
                    });
                  },
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
            ],
          ),
        ),

        // Child widget
        Expanded(child: widget.child),
      ],
    );
  }
}

/// A widget that shows a banner when the device is offline
class OfflineBannerWrapper extends ConsumerWidget {
  /// The child widget
  final Widget child;

  /// The message to display
  final String message;

  /// The action text
  final String actionText;

  /// The callback when the action button is pressed
  final VoidCallback? onAction;

  /// Whether the banner is dismissible
  final bool isDismissible;

  /// The position of the banner
  final OfflineBannerPosition position;

  /// Creates a new offline banner wrapper
  const OfflineBannerWrapper({
    super.key,
    required this.child,
    this.message = 'You are offline. Some features may be limited.',
    this.actionText = 'RETRY',
    this.onAction,
    this.isDismissible = true,
    this.position = OfflineBannerPosition.top,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the connectivity status
    final isOnlineAsync = ref.watch(connectivityStatusProvider);

    return isOnlineAsync.when(
      data: (isOnline) {
        // If online, just show the child
        if (isOnline) {
          return child;
        }

        // If offline, show the banner
        return _buildWithBanner();
      },
      loading: () => child,
      error: (_, __) => child,
    );
  }

  /// Build the widget with the offline banner
  Widget _buildWithBanner() {
    switch (position) {
      case OfflineBannerPosition.top:
        return Column(
          children: [
            OfflineBanner(
              message: message,
              actionText: actionText,
              onAction: onAction,
              isDismissible: isDismissible,
            ),
            Expanded(child: child),
          ],
        );
      case OfflineBannerPosition.bottom:
        return Column(
          children: [
            Expanded(child: child),
            OfflineBanner(
              message: message,
              actionText: actionText,
              onAction: onAction,
              isDismissible: isDismissible,
            ),
          ],
        );
      case OfflineBannerPosition.overlay:
        return Stack(
          children: [
            child,
            Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: OfflineBanner(
                message: message,
                actionText: actionText,
                onAction: onAction,
                isDismissible: isDismissible,
              ),
            ),
          ],
        );
    }
  }
}

/// The position of an offline banner
enum OfflineBannerPosition {
  /// Top of the screen
  top,

  /// Bottom of the screen
  bottom,

  /// Overlay on top of the screen
  overlay,
}
