import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/services/analytics/ux_metrics_service.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Main UX metrics dashboard widget
class UXMetricsWidget extends ConsumerStatefulWidget {
  /// Whether to show in compact mode
  final bool isCompact;

  /// Custom height for the widget
  final double? height;

  /// Whether to show refresh button
  final bool showRefreshButton;

  /// Callback when metric is tapped
  final void Function(String metricType)? onMetricTapped;

  /// Creates a new UX metrics widget
  const UXMetricsWidget({
    super.key,
    this.isCompact = false,
    thiseight,
    this.showRefreshButton = true,
    this.onMetricTapped,
  });

  @override
  ConsumerState<UXMetricsWidget> createState() => _UXMetricsWidgetState();
}

class _UXMetricsWidgetState extends ConsumerState<UXMetricsWidget>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  Map<String, dynamic>? _behaviorAnalysis;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _loadMetrics();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _loadMetrics() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final uxMetricsService = refead(uxMetricsServiceProvider);
      await uxMetricsService.initialize();

      final analysis = await uxMetricsService.analyzeUserBehavior();

      if (mounted) {
        setState(() {
          _behaviorAnalysis = analysis;
          _isLoading = false;
        });
        _fadeController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return _buildLoadingState(theme);
    }

    if (_error != null) {
      return _buildErrorState(theme);
    }

    if (_behaviorAnalysis == null || _behaviorAnalysis!.isEmpty) {
      return _buildEmptyState(theme);
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: widget.isCompact
          ? _buildCompactLayout(theme)
          : _buildFullLayout(theme),
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Container(
      height: widgeteight ?? (widget.isCompact ? 120 : 300),
      padding: EdgeInsets.all(AppThemeacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outlineithAlpha(51),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: AppThemeacingMedium),
            Text(
              'Loading UX metrics...',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme) {
    return Container(
      height: widgeteight ?? (widget.isCompact ? 120 : 300),
      padding: EdgeInsets.all(AppThemeacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainerithAlpha(26),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.errorithAlpha(51),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: theme.colorScheme.error,
              size: 32,
            ),
            const SizedBox(height: AppThemeacingSmall),
            Text(
              'Failed to load UX metrics',
              style: theme.textTheme.titleSmall?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: FontWeight600,
              ),
            ),
            if (widget.showRefreshButton) ...[
              const SizedBox(height: AppThemeacingMedium),
              TextButton.icon(
                onPressed: _loadMetrics,
                icon: const Icon(Iconsefresh),
                label: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      height: widgeteight ?? (widget.isCompact ? 120 : 300),
      padding: EdgeInsets.all(AppThemeacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outlineithAlpha(51),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_outlined,
              color: theme.colorScheme.onSurfaceVariant,
              size: 32,
            ),
            const SizedBox(height: AppThemeacingSmall),
            Text(
              'No UX metrics available',
              style: theme.textTheme.titleSmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight600,
              ),
            ),
            const SizedBox(height: AppThemeacingSmall),
            Text(
              'Start using the app to see UX insights',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactLayout(ThemeData theme) {
    final taskCompletionRate =
        _behaviorAnalysis!['taskCompletionRate'] as double? ?? 0.0;
    final averageSatisfactionRating =
        _behaviorAnalysis!['averageSatisfactionRating'] as double? ?? 0.0;
    final errorFrequency =
        _behaviorAnalysis!['errorFrequency'] as double? ?? 0.0;

    return Container(
      height: widgeteight ?? 120,
      padding: EdgeInsets.all(AppThemeacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outlineithAlpha(51),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildMetricCard(
              theme: theme,
              title: 'Task Success',
              value: '${(taskCompletionRate * 100).toStringAsFixed(0)}%',
              icon: Icons.task_alt,
              color: _getSuccessColor(theme, taskCompletionRate),
              isCompact: true,
              onTap: () => widget.onMetricTapped?.call('task_completion'),
            ),
          ),
          const SizedBox(width: AppThemeacingSmall),
          Expanded(
            child: _buildMetricCard(
              theme: theme,
              title: 'Satisfaction',
              value: averageSatisfactionRating.toStringAsFixed(1),
              icon: Icons.sentiment_satisfied,
              color: _getSatisfactionColor(theme, averageSatisfactionRating),
              isCompact: true,
              onTap: () => widget.onMetricTapped?.call('satisfaction'),
            ),
          ),
          const SizedBox(width: AppThemeacingSmall),
          Expanded(
            child: _buildMetricCard(
              theme: theme,
              title: 'Error Rate',
              value: '${(errorFrequency * 100).toStringAsFixed(1)}%',
              icon: Icons.error_outline,
              color: _getErrorColor(theme, errorFrequency),
              isCompact: true,
              onTap: () => widget.onMetricTapped?.call('errors'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullLayout(ThemeData theme) {
    return Container(
      height: widgeteight ?? 300,
      padding: EdgeInsets.all(AppThemeacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outlineithAlpha(51),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme),
          const SizedBox(height: AppThemeacingMedium),
          Expanded(
            child: _buildMetricsGrid(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Row(
      children: [
        Icon(
          Icons.analytics,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: AppThemeacingSmall),
        Text(
          'UX Metrics',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        if (widget.showRefreshButton)
          IconButton(
            onPressed: _loadMetrics,
            icon: Icon(
              Iconsefresh,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            tooltip: 'Refresh metrics',
          ),
      ],
    );
  }

  Widget _buildMetricsGrid(ThemeData theme) {
    final taskCompletionRate =
        _behaviorAnalysis!['taskCompletionRate'] as double? ?? 0.0;
    final averageSatisfactionRating =
        _behaviorAnalysis!['averageSatisfactionRating'] as double? ?? 0.0;
    final errorFrequency =
        _behaviorAnalysis!['errorFrequency'] as double? ?? 0.0;
    final totalMetrics = _behaviorAnalysis!['totalMetrics'] as int? ?? 0;

    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: AppThemeacingSmall,
      mainAxisSpacing: AppThemeacingSmall,
      childAspectRatio: 1.5,
      children: [
        _buildMetricCard(
          theme: theme,
          title: 'Task Completion Rate',
          value: '${(taskCompletionRate * 100).toStringAsFixed(0)}%',
          icon: Icons.task_alt,
          color: _getSuccessColor(theme, taskCompletionRate),
          subtitle: 'Success rate',
          onTap: () => widget.onMetricTapped?.call('task_completion'),
        ),
        _buildMetricCard(
          theme: theme,
          title: 'User Satisfaction',
          value: averageSatisfactionRating.toStringAsFixed(1),
          icon: Icons.sentiment_satisfied,
          color: _getSatisfactionColor(theme, averageSatisfactionRating),
          subtitle: 'Average rating',
          onTap: () => widget.onMetricTapped?.call('satisfaction'),
        ),
        _buildMetricCard(
          theme: theme,
          title: 'Error Frequency',
          value: '${(errorFrequency * 100).toStringAsFixed(1)}%',
          icon: Icons.error_outline,
          color: _getErrorColor(theme, errorFrequency),
          subtitle: 'Error rate',
          onTap: () => widget.onMetricTapped?.call('errors'),
        ),
        _buildMetricCard(
          theme: theme,
          title: 'Total Metrics',
          value: totalMetrics.toString(),
          icon: Icons.analytics_outlined,
          color: theme.colorScheme.primary,
          subtitle: 'Data points',
          onTap: () => widget.onMetricTapped?.call('total'),
        ),
      ],
    );
  }

  Widget _buildMetricCard({
    required ThemeData theme,
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
    bool isCompact = false,
    VoidCallback? onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        child: Container(
          padding: EdgeInsets.all(
              isCompact ? AppThemeacingSmall : AppThemeacingMedium),
          decoration: BoxDecoration(
            color: colorithAlpha(13),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            border: Border.all(
              color: colorithAlpha(51),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: colorithAlpha(26),
                      borderRadius:
                          BorderRadius.circular(AppTheme.borderRadiusSmall),
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: isCompact ? 16 : 20,
                    ),
                  ),
                  if (!isCompact) const Spacer(),
                ],
              ),
              SizedBox(
                  height: isCompact
                      ? AppThemeacingSmall
                      : AppThemeacingMedium),
              Text(
                value,
                style: (isCompact
                        ? theme.textTheme.titleMedium
                        : theme.textThemeeadlineSmall)
                    ?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              if (!isCompact) ...[
                const SizedBox(height: AppThemeacingSmall),
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight500,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ] else ...[
                Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getSuccessColor(ThemeData theme, double rate) {
    if (rate >= 0.8) return Colors.green;
    if (rate >= 0.6) return Colors.orange;
    return Colorsed;
  }

  Color _getSatisfactionColor(ThemeData theme, double rating) {
    if (rating >= 4.0) return Colors.green;
    if (rating >= 3.0) return Colors.orange;
    return Colorsed;
  }

  Color _getErrorColor(ThemeData theme, double frequency) {
    if (frequency <= 0.05) return Colors.green;
    if (frequency <= 0.1) return Colors.orange;
    return Colorsed;
  }
}

/// Journey visualization widget for user flow analysis
class JourneyVisualizationWidget extends ConsumerWidget {
  /// Height of the widget
  final double height;

  /// Whether to show in compact mode
  final bool isCompact;

  /// Creates a new journey visualization widget
  const JourneyVisualizationWidget({
    super.key,
    thiseight = 200,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Container(
      height: height,
      padding: EdgeInsets.all(AppThemeacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outlineithAlpha(51),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Iconsoute,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: AppThemeacingSmall),
              Text(
                'User Journey Flow',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppThemeacingMedium),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.timeline,
                    size: 48,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(height: AppThemeacingSmall),
                  Text(
                    'Journey visualization coming soon',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Conversion funnel widget for funnel analysis
class ConversionFunnelWidget extends ConsumerWidget {
  /// Height of the widget
  final double height;

  /// Whether to show in compact mode
  final bool isCompact;

  /// Creates a new conversion funnel widget
  const ConversionFunnelWidget({
    super.key,
    thiseight = 250,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Container(
      height: height,
      padding: EdgeInsets.all(AppThemeacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outlineithAlpha(51),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.filter_list,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: AppThemeacingSmall),
              Text(
                'Conversion Funnel',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppThemeacingMedium),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.insights,
                    size: 48,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(height: AppThemeacingSmall),
                  Text(
                    'Funnel analysis coming soon',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
