import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Stripe payment provider for diaspora markets
class StripePaymentProvider {
  final LoggingService _loggingService;
  bool _isInitialized = false;

  StripePaymentProvider({
    required LoggingService loggingService,
  }) : _loggingService = loggingService;

  /// Initialize Stripe SDK
  ///
  /// TODO: Backend Integration Required
  /// - Get publishable key from backend configuration
  /// - Environment-specific keys (test/live)
  /// - Merchant display name configuration
  Future<void> initialize({
    required String publishableKey,
    String? merchantDisplayName,
  }) async {
    try {
      Stripe.publishableKey = publishableKey;
      Stripe.merchantIdentifier = merchantDisplayName ?? 'CultureConnect';

      await Stripe.instance.applySettings();

      _isInitialized = true;

      _loggingService.info(
        'StripePaymentProvider',
        'Stripe SDK initialized successfully',
        {'merchantDisplayName': merchantDisplayName},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'StripePaymentProvider',
        'Failed to initialize Stripe SDK',
        {'error': e.toString()},
        stackTrace,
      );

      throw PaymentProviderException(
        'Failed to initialize Stripe: $e',
        provider: 'stripe',
        code: 'STRIPE_INIT_FAILED',
      );
    }
  }

  /// Process payment using Stripe Payment Sheet
  ///
  /// TODO: Backend Integration Required
  /// - Use client_secret from PaymentProviderConfig
  /// - Handle 3D Secure authentication flows
  /// - Return provider transaction ID for verification
  Future<StripePaymentResult> processPayment({
    required BuildContext context,
    required PaymentProviderConfig config,
    required String transactionReference,
  }) async {
    if (!_isInitialized) {
      throw PaymentProviderException(
        'Stripe provider not initialized',
        provider: 'stripe',
        code: 'STRIPE_NOT_INITIALIZED',
      );
    }

    if (config.provider != PaymentProvider.stripe ||
        config.clientSecret == null) {
      throw PaymentProviderException(
        'Invalid Stripe configuration',
        provider: 'stripe',
        code: 'STRIPE_INVALID_CONFIG',
      );
    }

    try {
      // Initialize Payment Sheet with backend configuration
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: config.clientSecret!,
          merchantDisplayName: 'CultureConnect',
          style: Theme.of(context).brightness == Brightness.dark
              ? ThemeMode.dark
              : ThemeMode.light,
          appearance: PaymentSheetAppearance(
            colors: PaymentSheetAppearanceColors(
              primary: Theme.of(context).colorScheme.primary,
              background: Theme.of(context).colorScheme.surface,
              componentBackground: Theme.of(context).colorScheme.surface,
            ),
            shapes: const PaymentSheetShape(
              borderRadius: 12,
              borderWidth: 0,
            ),
            primaryButton: PaymentSheetPrimaryButtonAppearance(
              colors: PaymentSheetPrimaryButtonTheme(
                light: PaymentSheetPrimaryButtonThemeColors(
                  background: Theme.of(context).colorScheme.primary,
                  text: Colors.white,
                ),
                dark: PaymentSheetPrimaryButtonThemeColors(
                  background: Theme.of(context).colorScheme.primary,
                  text: Colors.white,
                ),
              ),
            ),
          ),
        ),
      );

      _loggingService.info(
        'StripePaymentProvider',
        'Payment Sheet initialized',
        {'transactionReference': transactionReference},
      );

      // Present Payment Sheet
      await Stripe.instance.presentPaymentSheet();

      _loggingService.info(
        'StripePaymentProvider',
        'Payment completed successfully',
        {'transactionReference': transactionReference},
      );

      return StripePaymentResult(
        success: true,
        transactionReference: transactionReference,
        paymentIntentId: _extractPaymentIntentId(config.clientSecret!),
      );
    } on StripeException catch (e) {
      _loggingService.warning(
        'StripePaymentProvider',
        'Stripe payment failed',
        {
          'transactionReference': transactionReference,
          'error': e.error.localizedMessage,
          'code': e.error.code.name,
        },
      );

      // Handle user cancellation gracefully
      if (e.error.code == FailureCode.Canceled) {
        return StripePaymentResult(
          success: false,
          transactionReference: transactionReference,
          error: 'Payment cancelled by user',
          isCancelled: true,
        );
      }

      return StripePaymentResult(
        success: false,
        transactionReference: transactionReference,
        error: e.error.localizedMessage ?? 'Payment failed',
        stripeErrorCode: e.error.code.name,
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'StripePaymentProvider',
        'Unexpected error during Stripe payment',
        {
          'transactionReference': transactionReference,
          'error': e.toString(),
        },
        stackTrace,
      );

      return StripePaymentResult(
        success: false,
        transactionReference: transactionReference,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  /// Handle 3D Secure authentication if required
  ///
  /// TODO: Backend Integration Required
  /// - Handle authentication challenges
  /// - Update payment status based on authentication result
  Future<bool> handle3DSecure({
    required String paymentIntentId,
    required String transactionReference,
  }) async {
    try {
      final paymentIntent = await Stripe.instance.retrievePaymentIntent(
        paymentIntentId,
      );

      if (paymentIntent.status == PaymentIntentsStatus.RequiresAction) {
        await Stripe.instance.confirmPayment(
          paymentIntentId: paymentIntentId,
          data: const PaymentMethodData.card(
            paymentMethodData: PaymentMethodDataCard(),
          ),
        );

        _loggingService.info(
          'StripePaymentProvider',
          '3D Secure authentication completed',
          {'transactionReference': transactionReference},
        );

        return true;
      }

      return paymentIntent.status == PaymentIntentsStatus.Succeeded;
    } catch (e, stackTrace) {
      _loggingService.error(
        'StripePaymentProvider',
        'Failed to handle 3D Secure authentication',
        {
          'transactionReference': transactionReference,
          'error': e.toString(),
        },
        stackTrace,
      );

      return false;
    }
  }

  /// Extract Payment Intent ID from client secret
  String _extractPaymentIntentId(String clientSecret) {
    return clientSecret.split('_secret_').first;
  }

  /// Check if Stripe is available for the current device
  static bool isAvailable() {
    // Stripe is available on all platforms
    return true;
  }

  /// Get supported payment methods for Stripe
  static List<PaymentMethodType> getSupportedPaymentMethods() {
    return [
      PaymentMethodType.card,
    ];
  }

  /// Dispose resources
  void dispose() {
    // Stripe SDK doesn't require explicit disposal
    _isInitialized = false;
  }
}

/// Result model for Stripe payment operations
class StripePaymentResult {
  final bool success;
  final String transactionReference;
  final String? paymentIntentId;
  final String? error;
  final String? stripeErrorCode;
  final bool isCancelled;

  const StripePaymentResult({
    required this.success,
    required this.transactionReference,
    this.paymentIntentId,
    this.error,
    this.stripeErrorCode,
    this.isCancelled = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'transaction_reference': transactionReference,
      if (paymentIntentId != null) 'payment_intent_id': paymentIntentId,
      if (error != null) 'error': error,
      if (stripeErrorCode != null) 'stripe_error_code': stripeErrorCode,
      'is_cancelled': isCancelled,
    };
  }
}
