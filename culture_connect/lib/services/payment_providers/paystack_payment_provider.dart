import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_paystack/flutter_paystack.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Paystack payment provider for African markets
class PaystackPaymentProvider {
  final LoggingService _loggingService;
  final PaystackPlugin _plugin = PaystackPlugin();
  bool _isInitialized = false;

  PaystackPaymentProvider({
    required LoggingService loggingService,
  }) : _loggingService = loggingService;

  /// Initialize Paystack SDK
  ///
  /// TODO: Backend Integration Required
  /// - Get public key from backend configuration
  /// - Environment-specific keys (test/live)
  /// - Support for multiple African countries
  Future<void> initialize({
    required String publicKey,
  }) async {
    try {
      await _plugin.initialize(publicKey: publicKey);

      _isInitialized = true;

      _loggingService.info(
        'PaystackPaymentProvider',
        'Paystack SDK initialized successfully',
        {'publicKey': '${publicKey.substring(0, 8)}...'},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'PaystackPaymentProvider',
        'Failed to initialize Paystack SDK',
        {'error': e.toString()},
        stackTrace,
      );

      throw PaymentProviderException(
        'Failed to initialize Paystack: $e',
        provider: 'paystack',
        code: 'PAYSTACK_INIT_FAILED',
      );
    }
  }

  /// Process card payment using Paystack
  ///
  /// TODO: Backend Integration Required
  /// - Use authorization_url from PaymentProviderConfig
  /// - Handle multiple payment channels
  /// - Return provider transaction ID for verification
  Future<PaystackPaymentResult> processCardPayment({
    required BuildContext context,
    required PaymentProviderConfig config,
    required String transactionReference,
    required double amount,
    required String currency,
    required String userEmail,
  }) async {
    if (!_isInitialized) {
      throw const PaymentProviderException(
        'Paystack provider not initialized',
        provider: 'paystack',
        code: 'PAYSTACK_NOT_INITIALIZED',
      );
    }

    if (config.provider != PaymentProvider.paystack) {
      throw const PaymentProviderException(
        'Invalid Paystack configuration',
        provider: 'paystack',
        code: 'PAYSTACK_INVALID_CONFIG',
      );
    }

    try {
      // Create charge object
      final charge = Charge()
        ..amount = (amount * 100).toInt() // Convert to kobo
        ..reference = transactionReference
        ..email = userEmail
        ..currency = currency;

      _loggingService.info(
        'PaystackPaymentProvider',
        'Processing card payment',
        {
          'transactionReference': transactionReference,
          'amount': amount,
          'currency': currency,
        },
      );

      // Process payment using Paystack Drop-in UI
      final response = await _plugin.chargeCard(context, charge: charge);

      if (response.status) {
        _loggingService.info(
          'PaystackPaymentProvider',
          'Card payment completed successfully',
          {
            'transactionReference': transactionReference,
            'reference': response.reference,
          },
        );

        return PaystackPaymentResult(
          success: true,
          transactionReference: transactionReference,
          paystackReference: response.reference,
          message: response.message,
        );
      } else {
        _loggingService.warning(
          'PaystackPaymentProvider',
          'Card payment failed',
          {
            'transactionReference': transactionReference,
            'message': response.message,
          },
        );

        return PaystackPaymentResult(
          success: false,
          transactionReference: transactionReference,
          error: response.message,
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'PaystackPaymentProvider',
        'Unexpected error during card payment',
        {
          'transactionReference': transactionReference,
          'error': e.toString(),
        },
        stackTrace,
      );

      return PaystackPaymentResult(
        success: false,
        transactionReference: transactionReference,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  /// Process bank transfer payment using Paystack
  ///
  /// TODO: Backend Integration Required
  /// - Support for Nigerian banks
  /// - Handle bank selection UI
  /// - Provide bank account details for transfer
  Future<PaystackPaymentResult> processBankTransfer({
    required BuildContext context,
    required PaymentProviderConfig config,
    required String transactionReference,
    required double amount,
    required String currency,
    required String userEmail,
  }) async {
    if (!_isInitialized) {
      throw const PaymentProviderException(
        'Paystack provider not initialized',
        provider: 'paystack',
        code: 'PAYSTACK_NOT_INITIALIZED',
      );
    }

    try {
      // Create charge object for bank transfer
      final charge = Charge()
        ..amount = (amount * 100).toInt()
        ..reference = transactionReference
        ..email = userEmail
        ..currency = currency;

      _loggingService.info(
        'PaystackPaymentProvider',
        'Processing bank transfer',
        {
          'transactionReference': transactionReference,
          'amount': amount,
          'currency': currency,
        },
      );

      // Process bank transfer using Paystack
      final response = await _plugin.chargeCard(context, charge: charge);

      if (response.status) {
        _loggingService.info(
          'PaystackPaymentProvider',
          'Bank transfer initiated successfully',
          {
            'transactionReference': transactionReference,
            'reference': response.reference,
          },
        );

        return PaystackPaymentResult(
          success: true,
          transactionReference: transactionReference,
          paystackReference: response.reference,
          message: response.message,
          requiresConfirmation: true,
        );
      } else {
        return PaystackPaymentResult(
          success: false,
          transactionReference: transactionReference,
          error: response.message,
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'PaystackPaymentProvider',
        'Unexpected error during bank transfer',
        {
          'transactionReference': transactionReference,
          'error': e.toString(),
        },
        stackTrace,
      );

      return PaystackPaymentResult(
        success: false,
        transactionReference: transactionReference,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  /// Process USSD payment using Paystack
  ///
  /// TODO: Backend Integration Required
  /// - Support for Nigerian USSD codes
  /// - Display USSD code to user
  /// - Handle USSD confirmation flow
  Future<PaystackPaymentResult> processUssdPayment({
    required BuildContext context,
    required PaymentProviderConfig config,
    required String transactionReference,
    required double amount,
    required String currency,
    required String userEmail,
  }) async {
    if (!_isInitialized) {
      throw const PaymentProviderException(
        'Paystack provider not initialized',
        provider: 'paystack',
        code: 'PAYSTACK_NOT_INITIALIZED',
      );
    }

    try {
      // Create charge object for USSD
      final charge = Charge()
        ..amount = (amount * 100).toInt()
        ..reference = transactionReference
        ..email = userEmail
        ..currency = currency;

      _loggingService.info(
        'PaystackPaymentProvider',
        'Processing USSD payment',
        {
          'transactionReference': transactionReference,
          'amount': amount,
          'currency': currency,
        },
      );

      // Process USSD payment using Paystack
      final response = await _plugin.chargeCard(context, charge: charge);

      if (response.status) {
        _loggingService.info(
          'PaystackPaymentProvider',
          'USSD payment initiated successfully',
          {
            'transactionReference': transactionReference,
            'reference': response.reference,
          },
        );

        return PaystackPaymentResult(
          success: true,
          transactionReference: transactionReference,
          paystackReference: response.reference,
          message: response.message,
          requiresConfirmation: true,
          ussdCode: response.message, // USSD code is typically in the message
        );
      } else {
        return PaystackPaymentResult(
          success: false,
          transactionReference: transactionReference,
          error: response.message,
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'PaystackPaymentProvider',
        'Unexpected error during USSD payment',
        {
          'transactionReference': transactionReference,
          'error': e.toString(),
        },
        stackTrace,
      );

      return PaystackPaymentResult(
        success: false,
        transactionReference: transactionReference,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  /// Check if Paystack is available for the current device
  static bool isAvailable() {
    // Paystack is available on all platforms
    return true;
  }

  /// Get supported payment methods for Paystack
  static List<PaymentMethodType> getSupportedPaymentMethods() {
    return [
      PaymentMethodType.card,
      PaymentMethodType.bankTransfer,
      PaymentMethodType.ussd,
      PaymentMethodType.mobileMoney,
    ];
  }

  /// Dispose resources
  void dispose() {
    // Paystack plugin doesn't require explicit disposal
    _isInitialized = false;
  }
}

/// Result model for Paystack payment operations
class PaystackPaymentResult {
  final bool success;
  final String transactionReference;
  final String? paystackReference;
  final String? message;
  final String? error;
  final bool requiresConfirmation;
  final String? ussdCode;

  const PaystackPaymentResult({
    required this.success,
    required this.transactionReference,
    this.paystackReference,
    this.message,
    this.error,
    this.requiresConfirmation = false,
    this.ussdCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'transaction_reference': transactionReference,
      if (paystackReference != null) 'paystack_reference': paystackReference,
      if (message != null) 'message': message,
      if (error != null) 'error': error,
      'requires_confirmation': requiresConfirmation,
      if (ussdCode != null) 'ussd_code': ussdCode,
    };
  }
}
