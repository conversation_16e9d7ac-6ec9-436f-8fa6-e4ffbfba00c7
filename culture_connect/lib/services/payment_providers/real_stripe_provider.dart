import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Production-ready Stripe payment provider with actual Payment Sheet integration
class RealStripeProvider {
  final LoggingService _loggingService;
  bool _isInitialized = false;
  String? _publishableKey;
  String? _merchantDisplayName;

  RealStripeProvider({
    required LoggingService loggingService,
  }) : _loggingService = loggingService;

  /// Initialize Stripe SDK with production configuration
  Future<void> initialize({
    required String publishableKey,
    String? merchantDisplayName,
  }) async {
    try {
      _publishableKey = publishableKey;
      _merchantDisplayName = merchantDisplayName ?? 'CultureConnect';

      // Configure Stripe with publishable key
      Stripe.publishableKey = publishableKey;
      Stripe.merchantIdentifier = _merchantDisplayName!;

      // Apply Stripe settings
      await Stripe.instance.applySettings();

      _isInitialized = true;

      _loggingService.info(
        'RealStripeProvider',
        'Stripe provider initialized successfully',
        {
          'merchant_name': _merchantDisplayName,
          'key_prefix': publishableKey.substring(0, 12),
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'RealStripeProvider',
        'Failed to initialize Stripe provider',
        {'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Process payment using Stripe Payment Sheet
  Future<StripePaymentResult> processPayment({
    required BuildContext context,
    required PaymentProviderConfig config,
    required String transactionReference,
  }) async {
    if (!_isInitialized) {
      throw const PaymentProviderException(
        'Stripe provider not initialized',
        provider: 'stripe',
        code: 'STRIPE_NOT_INITIALIZED',
      );
    }

    if (config.provider != PaymentProvider.stripe ||
        config.clientSecret == null) {
      throw const PaymentProviderException(
        'Invalid Stripe configuration',
        provider: 'stripe',
        code: 'STRIPE_INVALID_CONFIG',
      );
    }

    try {
      // Initialize Payment Sheet with backend configuration
      await _initializePaymentSheet(context, config);

      // Present Payment Sheet to user
      await Stripe.instance.presentPaymentSheet();

      // Payment successful - provide haptic feedback
      HapticFeedback.lightImpact();

      _loggingService.info(
        'RealStripeProvider',
        'Stripe payment completed successfully',
        {
          'transaction_reference': transactionReference,
          'payment_intent_id': _extractPaymentIntentId(config.clientSecret!),
        },
      );

      return StripePaymentResult(
        success: true,
        transactionReference: transactionReference,
        paymentIntentId: _extractPaymentIntentId(config.clientSecret!),
      );
    } on StripeException catch (e) {
      _loggingService.warning(
        'RealStripeProvider',
        'Stripe payment failed',
        {
          'transaction_reference': transactionReference,
          'error_code': e.error.code.name,
          'error_message': e.error.message,
        },
      );

      // Handle user cancellation gracefully
      if (e.error.code == FailureCode.Canceled) {
        return StripePaymentResult(
          success: false,
          transactionReference: transactionReference,
          errorMessage: 'Payment was cancelled by user',
          isCancelled: true,
        );
      }

      // Handle other Stripe errors
      return StripePaymentResult(
        success: false,
        transactionReference: transactionReference,
        errorMessage: e.error.message ?? 'Payment failed',
        errorCode: e.error.code.name,
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'RealStripeProvider',
        'Unexpected error during Stripe payment',
        {
          'transaction_reference': transactionReference,
          'error': e.toString(),
        },
        stackTrace,
      );

      return StripePaymentResult(
        success: false,
        transactionReference: transactionReference,
        errorMessage: 'An unexpected error occurred: $e',
      );
    }
  }

  /// Initialize Payment Sheet with Material Design 3 theming
  Future<void> _initializePaymentSheet(
    BuildContext context,
    PaymentProviderConfig config,
  ) async {
    try {
      final theme = Theme.of(context);

      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: config.clientSecret!,
          merchantDisplayName: _merchantDisplayName!,
          style: theme.brightness == Brightness.dark
              ? ThemeMode.dark
              : ThemeMode.light,
          appearance: PaymentSheetAppearance(
            colors: PaymentSheetAppearanceColors(
              primary: theme.colorScheme.primary,
              background: theme.colorScheme.surface,
              componentBackground: theme.colorScheme.surfaceContainerHighest,
              componentBorder: theme.colorScheme.outline,
              componentDivider: theme.colorScheme.outlineVariant,
              primaryText: theme.colorScheme.onSurface,
              secondaryText: theme.colorScheme.onSurfaceVariant,
              componentText: theme.colorScheme.onSurfaceVariant,
              placeholderText:
                  theme.colorScheme.onSurfaceVariant.withAlpha(153),
              icon: theme.colorScheme.onSurfaceVariant,
              error: theme.colorScheme.error,
            ),
            shapes: const PaymentSheetShape(
              borderRadius: 12,
              borderWidth: 0,
            ),
            primaryButton: PaymentSheetPrimaryButtonAppearance(
              colors: PaymentSheetPrimaryButtonTheme(
                light: PaymentSheetPrimaryButtonThemeColors(
                  background: theme.colorScheme.primary,
                  text: theme.colorScheme.onPrimary,
                  border: theme.colorScheme.primary,
                ),
                dark: PaymentSheetPrimaryButtonThemeColors(
                  background: theme.colorScheme.primary,
                  text: theme.colorScheme.onPrimary,
                  border: theme.colorScheme.primary,
                ),
              ),
              shapes: const PaymentSheetPrimaryButtonShape(
                borderRadius: 8,
                borderWidth: 0,
              ),
            ),
          ),
          allowsDelayedPaymentMethods: true,
          googlePay: const PaymentSheetGooglePay(
            merchantCountryCode: 'US',
            currencyCode: 'USD',
            testEnv: false, // Set based on environment
          ),
          applePay: const PaymentSheetApplePay(
            merchantCountryCode: 'US',
          ),
        ),
      );

      _loggingService.info(
        'RealStripeProvider',
        'Payment Sheet initialized successfully',
        {
          'payment_intent_id': _extractPaymentIntentId(config.clientSecret!),
          'theme_mode': theme.brightness.name,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'RealStripeProvider',
        'Failed to initialize Payment Sheet',
        {'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Handle 3D Secure authentication if required
  Future<bool> handle3DSecure({
    required String paymentIntentId,
    required String transactionReference,
  }) async {
    try {
      final paymentIntent = await Stripe.instance.retrievePaymentIntent(
        paymentIntentId,
      );

      if (paymentIntent.status == PaymentIntentsStatus.RequiresAction) {
        await Stripe.instance.confirmPayment(
          paymentIntentClientSecret: paymentIntent.clientSecret,
        );

        _loggingService.info(
          'RealStripeProvider',
          '3D Secure authentication completed',
          {
            'payment_intent_id': paymentIntentId,
            'transaction_reference': transactionReference,
          },
        );

        return true;
      }

      return paymentIntent.status == PaymentIntentsStatus.Succeeded;
    } catch (e, stackTrace) {
      _loggingService.error(
        'RealStripeProvider',
        'Failed to handle 3D Secure authentication',
        {
          'payment_intent_id': paymentIntentId,
          'error': e.toString(),
        },
        stackTrace,
      );
      return false;
    }
  }

  /// Verify payment status with Stripe
  Future<bool> verifyPayment(String paymentIntentId) async {
    try {
      final paymentIntent = await Stripe.instance.retrievePaymentIntent(
        paymentIntentId,
      );

      final isSuccessful =
          paymentIntent.status == PaymentIntentsStatus.Succeeded;

      _loggingService.info(
        'RealStripeProvider',
        'Payment verification completed',
        {
          'payment_intent_id': paymentIntentId,
          'status': paymentIntent.status.name,
          'is_successful': isSuccessful,
        },
      );

      return isSuccessful;
    } catch (e, stackTrace) {
      _loggingService.error(
        'RealStripeProvider',
        'Failed to verify payment',
        {'payment_intent_id': paymentIntentId, 'error': e.toString()},
        stackTrace,
      );
      return false;
    }
  }

  /// Extract Payment Intent ID from client secret
  String _extractPaymentIntentId(String clientSecret) {
    return clientSecret.split('_secret_').first;
  }

  /// Check if provider is initialized
  bool get isInitialized => _isInitialized;

  /// Get current publishable key prefix (for debugging)
  String? get publishableKeyPrefix => _publishableKey?.substring(0, 12);

  /// Dispose resources
  void dispose() {
    _isInitialized = false;
    _publishableKey = null;
    _merchantDisplayName = null;
  }
}

/// Stripe payment result model
class StripePaymentResult {
  final bool success;
  final String transactionReference;
  final String? paymentIntentId;
  final String? errorMessage;
  final String? errorCode;
  final bool isCancelled;

  const StripePaymentResult({
    required this.success,
    required this.transactionReference,
    this.paymentIntentId,
    this.errorMessage,
    this.errorCode,
    this.isCancelled = false,
  });

  @override
  String toString() {
    return 'StripePaymentResult('
        'success: $success, '
        'transactionReference: $transactionReference, '
        'paymentIntentId: $paymentIntentId, '
        'errorMessage: $errorMessage, '
        'errorCode: $errorCode, '
        'isCancelled: $isCancelled'
        ')';
  }
}
