import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/payment_api_service.dart';
import 'package:culture_connect/services/payment_providers/real_stripe_provider.dart';
import 'package:culture_connect/services/payment_providers/paystack_payment_provider.dart';
import 'package:culture_connect/services/payment_providers/busha_payment_provider.dart';
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/services/mascot_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Enhanced payment service with production backend integration
class EnhancedPaymentService {
  final PaymentApiService _apiService;
  final RealStripeProvider _stripeProvider;
  final PaystackPaymentProvider _paystackProvider;
  final BushaPaymentProvider _bushaProvider;
  final AchievementService _achievementService;
  final MascotService _mascotService;
  final AnalyticsService _analyticsService;
  final LoggingService _loggingService;

  bool _isInitialized = false;
  PaymentInitResponse? _currentPaymentInit;

  EnhancedPaymentService({
    required PaymentApiService apiService,
    required RealStripeProvider stripeProvider,
    required PaystackPaymentProvider paystackProvider,
    required BushaPaymentProvider bushaProvider,
    required AchievementService achievementService,
    required MascotService mascotService,
    required AnalyticsService analyticsService,
    required LoggingService loggingService,
  })  : _apiService = apiService,
        _stripeProvider = stripeProvider,
        _paystackProvider = paystackProvider,
        _bushaProvider = bushaProvider,
        _achievementService = achievementService,
        _mascotService = mascotService,
        _analyticsService = analyticsService,
        _loggingService = loggingService;

  /// Initialize the enhanced payment service
  ///
  /// TODO: Backend Integration Required
  /// - Get provider configurations from backend
  /// - Initialize all payment providers with correct keys
  /// - Set up authentication tokens
  Future<void> initialize({
    required String authToken,
  }) async {
    if (_isInitialized) return;

    try {
      // Set authentication token
      _apiService.setAuthToken(authToken);

      // TODO: Get provider configurations from backend
      // final config = await _apiService.getProviderConfigurations();

      // Initialize payment providers with backend configurations
      await _stripeProvider.initialize(
        publishableKey: 'pk_test_...', // TODO: Get from backend
        merchantDisplayName: 'CultureConnect',
      );

      await _paystackProvider.initialize(
        publicKey: 'pk_test_...', // TODO: Get from backend
      );

      await _bushaProvider.initialize();

      _isInitialized = true;

      _loggingService.info(
        'EnhancedPaymentService',
        'Payment service initialized successfully',
        {},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'EnhancedPaymentService',
        'Failed to initialize payment service',
        {'error': e.toString()},
        stackTrace,
      );

      throw PaymentException(
        'Failed to initialize payment service: $e',
        code: 'PAYMENT_INIT_FAILED',
      );
    }
  }

  /// Process payment with automatic provider selection and integration
  Future<EnhancedPaymentResult> processPayment({
    required BuildContext context,
    required Booking booking,
    required String userEmail,
    required String userName,
    String? userPhone,
    PaymentMethodType? preferredMethod,
  }) async {
    if (!_isInitialized) {
      throw const PaymentException(
        'Payment service not initialized',
        code: 'PAYMENT_NOT_INITIALIZED',
      );
    }

    final startTime = DateTime.now();

    try {
      // Update mascot to helpful expression during processing
      await _mascotService.updateState(
        _mascotService.getStateForTravelAction(
          actionType: 'payment',
          isSuccess: false,
          message: 'Processing your payment...',
        ),
      );

      // Get user location for geolocation-aware routing
      final location = await _getUserLocation();

      // Initialize payment with backend
      final initRequest = PaymentInitRequest(
        bookingId: booking.id,
        amount: booking.totalAmount,
        currency: 'USD', // TODO: Get from booking or user preferences
        userEmail: userEmail,
        userName: userName,
        userPhone: userPhone,
        countryCode: location?.countryCode,
        latitude: location?.latitude,
        longitude: location?.longitude,
        metadata: {
          'booking_type': 'experience',
          'experience_id': booking.experienceId,
          'participant_count': booking.participantCount,
        },
      );

      _currentPaymentInit = await _apiService.initializePayment(initRequest);

      _loggingService.info(
        'EnhancedPaymentService',
        'Payment initialized',
        {
          'transactionReference': _currentPaymentInit!.transactionReference,
          'selectedProvider': _currentPaymentInit!.selectedProvider.name,
          'correlationId': _currentPaymentInit!.correlationId,
        },
      );

      // Process payment with selected provider
      final providerResult = await _processWithProvider(
        context: context,
        config: _currentPaymentInit!.providerConfig,
        transactionReference: _currentPaymentInit!.transactionReference,
        amount: booking.totalAmount,
        currency: 'USD',
        userEmail: userEmail,
      );

      if (providerResult.success) {
        // Verify payment with backend
        final verificationRequest = PaymentVerificationRequest(
          transactionReference: _currentPaymentInit!.transactionReference,
          providerTransactionId: providerResult.providerTransactionId,
          correlationId: _currentPaymentInit!.correlationId,
        );

        final verificationResult =
            await _apiService.verifyPayment(verificationRequest);

        if (verificationResult.isSuccessful) {
          // Track achievement for successful payment
          await _achievementService.trackUserAction(
            UserAction.firstBooking,
            metadata: {
              'amount': booking.totalAmount,
              'provider': _currentPaymentInit!.selectedProvider.name,
              'booking_type': 'experience',
            },
          );

          // Update mascot to celebrating expression
          await _mascotService.updateState(
            _mascotService.getStateForTravelAction(
              actionType: 'payment',
              isSuccess: true,
              metadata: {
                'bookingId': booking.id,
                'amount': booking.totalAmount,
              },
            ),
          );

          // Track analytics
          await _analyticsService.logEvent(
            name: 'payment_completed',
            category: AnalyticsCategory.payment,
            parameters: {
              'transaction_reference':
                  _currentPaymentInit!.transactionReference,
              'provider': _currentPaymentInit!.selectedProvider.name,
              'amount': booking.totalAmount,
              'processing_time_ms':
                  DateTime.now().difference(startTime).inMilliseconds,
            },
          );

          return EnhancedPaymentResult(
            success: true,
            transactionReference: _currentPaymentInit!.transactionReference,
            provider: _currentPaymentInit!.selectedProvider,
            receiptId: verificationResult.receiptId,
            processingTime: DateTime.now().difference(startTime),
          );
        } else {
          throw PaymentException(
            'Payment verification failed: ${verificationResult.failureReason}',
            code: 'PAYMENT_VERIFICATION_FAILED',
          );
        }
      } else {
        // Handle payment failure
        await _mascotService.updateState(
          _mascotService.getStateForTravelAction(
            actionType: 'payment',
            isSuccess: false,
            message: 'Payment failed. Please try again.',
          ),
        );

        return EnhancedPaymentResult(
          success: false,
          transactionReference: _currentPaymentInit!.transactionReference,
          provider: _currentPaymentInit!.selectedProvider,
          error: providerResult.error,
          processingTime: DateTime.now().difference(startTime),
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'EnhancedPaymentService',
        'Payment processing failed',
        {
          'bookingId': booking.id,
          'error': e.toString(),
        },
        stackTrace,
      );

      // Update mascot to sympathetic expression
      await _mascotService.updateState(
        _mascotService.getStateForTravelAction(
          actionType: 'payment',
          isSuccess: false,
          message: 'Something went wrong. Please try again.',
        ),
      );

      return EnhancedPaymentResult(
        success: false,
        transactionReference: _currentPaymentInit?.transactionReference,
        provider: _currentPaymentInit?.selectedProvider,
        error: e.toString(),
        processingTime: DateTime.now().difference(startTime),
      );
    }
  }

  /// Process payment with the appropriate provider
  Future<ProviderPaymentResult> _processWithProvider({
    required BuildContext context,
    required PaymentProviderConfig config,
    required String transactionReference,
    required double amount,
    required String currency,
    required String userEmail,
  }) async {
    switch (config.provider) {
      case PaymentProvider.stripe:
        final result = await _stripeProvider.processPayment(
          context: context,
          config: config,
          transactionReference: transactionReference,
        );
        return ProviderPaymentResult(
          success: result.success,
          providerTransactionId: result.paymentIntentId,
          error: result.errorMessage,
        );

      case PaymentProvider.paystack:
        final result = await _paystackProvider.processCardPayment(
          context: context,
          config: config,
          transactionReference: transactionReference,
          amount: amount,
          currency: currency,
          userEmail: userEmail,
        );
        return ProviderPaymentResult(
          success: result.success,
          providerTransactionId: result.paystackReference,
          error: result.error,
        );

      case PaymentProvider.busha:
        final result = await _bushaProvider.processCryptoPayment(
          config: config,
          transactionReference: transactionReference,
          onStatusUpdate: (status) {
            // Handle real-time status updates
            _loggingService.info(
              'EnhancedPaymentService',
              'Crypto payment status update',
              {
                'transactionReference': transactionReference,
                'status': status.name,
              },
            );
          },
        );
        return ProviderPaymentResult(
          success: result.success,
          providerTransactionId: result.walletAddress,
          error: result.error,
        );
    }
  }

  /// Get user location for geolocation-aware payment routing
  Future<GeolocationData?> _getUserLocation() async {
    try {
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        final requestResult = await Geolocator.requestPermission();
        if (requestResult == LocationPermission.denied) {
          return null;
        }
      }

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.low,
        timeLimit: const Duration(seconds: 10),
      );

      // TODO: Convert coordinates to country/region using backend geocoding
      return GeolocationData(
        countryCode: 'US', // TODO: Get from geocoding
        countryName: 'United States', // TODO: Get from geocoding
        region: 'North America', // TODO: Get from geocoding
        city: 'Unknown', // TODO: Get from geocoding
        latitude: position.latitude,
        longitude: position.longitude,
        confidence: 0.8,
        isVpnDetected: false,
        recommendedProvider: 'stripe',
      );
    } catch (e) {
      _loggingService.warning(
        'EnhancedPaymentService',
        'Failed to get user location',
        {'error': e.toString()},
      );
      return null;
    }
  }

  /// Get current payment initialization data
  PaymentInitResponse? get currentPaymentInit => _currentPaymentInit;

  /// Dispose resources
  void dispose() {
    _stripeProvider.dispose();
    _paystackProvider.dispose();
    _bushaProvider.dispose();
    _apiService.dispose();
    _isInitialized = false;
  }
}

/// Result model for enhanced payment operations
class EnhancedPaymentResult {
  final bool success;
  final String? transactionReference;
  final PaymentProvider? provider;
  final String? receiptId;
  final String? error;
  final Duration processingTime;

  const EnhancedPaymentResult({
    required this.success,
    this.transactionReference,
    this.provider,
    this.receiptId,
    this.error,
    required this.processingTime,
  });
}

/// Internal result model for provider operations
class ProviderPaymentResult {
  final bool success;
  final String? providerTransactionId;
  final String? error;

  const ProviderPaymentResult({
    required this.success,
    this.providerTransactionId,
    this.error,
  });
}
