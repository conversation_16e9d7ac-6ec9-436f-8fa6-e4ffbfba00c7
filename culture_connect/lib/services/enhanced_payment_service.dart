import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/payment_api_service.dart';
import 'package:culture_connect/services/payment_providers/real_stripe_provider.dart';
import 'package:culture_connect/services/payment_providers/paystack_payment_provider.dart';
import 'package:culture_connect/services/payment_providers/busha_payment_provider.dart';
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/services/mascot_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/logging_service.dart';

import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Enhanced payment service with production backend integration
class EnhancedPaymentService {
  final PaymentApiService _apiService;
  final RealStripeProvider _stripeProvider;
  final PaystackPaymentProvider _paystackProvider;
  final BushaPaymentProvider _bushaProvider;
  final AchievementService _achievementService;
  final MascotService _mascotService;
  final AnalyticsService _analyticsService;
  final LoggingService _loggingService;

  bool _isInitialized = false;
  PaymentInitResponse? _currentPaymentInit;

  EnhancedPaymentService({
    required PaymentApiService apiService,
    required RealStripeProvider stripeProvider,
    required PaystackPaymentProvider paystackProvider,
    required BushaPaymentProvider bushaProvider,
    required AchievementService achievementService,
    required MascotService mascotService,
    required AnalyticsService analyticsService,
    required LoggingService loggingService,
  })  : _apiService = apiService,
        _stripeProvider = stripeProvider,
        _paystackProvider = paystackProvider,
        _bushaProvider = bushaProvider,
        _achievementService = achievementService,
        _mascotService = mascotService,
        _analyticsService = analyticsService,
        _loggingService = loggingService;

  /// Initialize the enhanced payment service
  ///
  /// TODO: Backend Integration Required
  /// - Get provider configurations from backend
  /// - Initialize all payment providers with correct keys
  /// - Set up authentication tokens
  Future<void> initialize({
    required String authToken,
  }) async {
    if (_isInitialized) return;

    try {
      // Set authentication token
      _apiService.setAuthToken(authToken);

      // TODO: Get provider configurations from backend
      // final config = await _apiService.getProviderConfigurations();

      // Initialize payment providers with backend configurations
      await _stripeProvider.initialize(
        publishableKey: 'pk_test_...', // TODO: Get from backend
        merchantDisplayName: 'CultureConnect',
      );

      await _paystackProvider.initialize(
        publicKey: 'pk_test_...', // TODO: Get from backend
      );

      await _bushaProvider.initialize();

      _isInitialized = true;

      _loggingService.info(
        'EnhancedPaymentService',
        'Payment service initialized successfully',
        {},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'EnhancedPaymentService',
        'Failed to initialize payment service',
        {'error': e.toString()},
        stackTrace,
      );

      throw PaymentException(
        'Failed to initialize payment service: $e',
        code: 'PAYMENT_INIT_FAILED',
      );
    }
  }

  /// Process payment with automatic provider selection and integration
  Future<EnhancedPaymentResult> processPayment({
    required BuildContext context,
    required Booking booking,
    required String userEmail,
    required String userName,
    String? userPhone,
    PaymentMethodType? preferredMethod,
  }) async {
    if (!_isInitialized) {
      throw const PaymentException(
        'Payment service not initialized',
        code: 'PAYMENT_NOT_INITIALIZED',
      );
    }

    final startTime = DateTime.now();

    try {
      // Update mascot to helpful expression during processing
      await _mascotService.updateState(
        _mascotService.getStateForTravelAction(
          actionType: 'payment',
          isSuccess: false,
          message: 'Processing your payment...',
        ),
      );

      // Get user location for geolocation-aware routing
      final location = await _getUserLocation();

      // Initialize payment with backend
      final initRequest = PaymentInitRequest(
        bookingId: booking.id,
        amount: booking.totalAmount,
        currency: 'USD', // TODO: Get from booking or user preferences
        userEmail: userEmail,
        userName: userName,
        userPhone: userPhone,
        countryCode: location?.countryCode,
        latitude: location?.latitude,
        longitude: location?.longitude,
        metadata: {
          'booking_type': 'experience',
          'experience_id': booking.experienceId,
          'participant_count': booking.participantCount,
        },
      );

      _currentPaymentInit = await _apiService.initializePayment(initRequest);

      _loggingService.info(
        'EnhancedPaymentService',
        'Payment initialized',
        {
          'transactionReference': _currentPaymentInit!.transactionReference,
          'selectedProvider': _currentPaymentInit!.selectedProvider.name,
          'correlationId': _currentPaymentInit!.correlationId,
        },
      );

      // Process payment with selected provider
      // Store context validity before async operation
      final isContextValid = context.mounted;

      if (!isContextValid) {
        throw const PaymentException(
          'Payment context is no longer valid',
          code: 'PAYMENT_CONTEXT_INVALID',
        );
      }

      final providerResult = await _processWithProvider(
        context: context,
        config: _currentPaymentInit!.providerConfig,
        transactionReference: _currentPaymentInit!.transactionReference,
        amount: booking.totalAmount,
        currency: 'USD',
        userEmail: userEmail,
      );

      if (providerResult.success) {
        // Verify payment with backend
        final verificationRequest = PaymentVerificationRequest(
          transactionReference: _currentPaymentInit!.transactionReference,
          providerTransactionId: providerResult.providerTransactionId,
          correlationId: _currentPaymentInit!.correlationId,
        );

        final verificationResult =
            await _apiService.verifyPayment(verificationRequest);

        if (verificationResult.isSuccessful) {
          // Track achievement for successful payment
          await _achievementService.trackUserAction(
            UserAction.firstBooking,
            metadata: {
              'amount': booking.totalAmount,
              'provider': _currentPaymentInit!.selectedProvider.name,
              'booking_type': 'experience',
            },
          );

          // Update mascot to celebrating expression
          await _mascotService.updateState(
            _mascotService.getStateForTravelAction(
              actionType: 'payment',
              isSuccess: true,
              metadata: {
                'bookingId': booking.id,
                'amount': booking.totalAmount,
              },
            ),
          );

          // Track analytics
          await _analyticsService.logEvent(
            name: 'payment_completed',
            category: AnalyticsCategory.payment,
            parameters: {
              'transaction_reference':
                  _currentPaymentInit!.transactionReference,
              'provider': _currentPaymentInit!.selectedProvider.name,
              'amount': booking.totalAmount,
              'processing_time_ms':
                  DateTime.now().difference(startTime).inMilliseconds,
            },
          );

          return EnhancedPaymentResult(
            success: true,
            transactionReference: _currentPaymentInit!.transactionReference,
            provider: _currentPaymentInit!.selectedProvider,
            receiptId: verificationResult.receiptId,
            processingTime: DateTime.now().difference(startTime),
          );
        } else {
          throw PaymentException(
            'Payment verification failed: ${verificationResult.failureReason}',
            code: 'PAYMENT_VERIFICATION_FAILED',
          );
        }
      } else {
        // Handle payment failure
        await _mascotService.updateState(
          _mascotService.getStateForTravelAction(
            actionType: 'payment',
            isSuccess: false,
            message: 'Payment failed. Please try again.',
          ),
        );

        return EnhancedPaymentResult(
          success: false,
          transactionReference: _currentPaymentInit!.transactionReference,
          provider: _currentPaymentInit!.selectedProvider,
          error: providerResult.error,
          processingTime: DateTime.now().difference(startTime),
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'EnhancedPaymentService',
        'Payment processing failed',
        {
          'bookingId': booking.id,
          'error': e.toString(),
        },
        stackTrace,
      );

      // Update mascot to sympathetic expression
      await _mascotService.updateState(
        _mascotService.getStateForTravelAction(
          actionType: 'payment',
          isSuccess: false,
          message: 'Something went wrong. Please try again.',
        ),
      );

      return EnhancedPaymentResult(
        success: false,
        transactionReference: _currentPaymentInit?.transactionReference,
        provider: _currentPaymentInit?.selectedProvider,
        error: e.toString(),
        processingTime: DateTime.now().difference(startTime),
      );
    }
  }

  /// Process payment with the appropriate provider
  Future<ProviderPaymentResult> _processWithProvider({
    required BuildContext context,
    required PaymentProviderConfig config,
    required String transactionReference,
    required double amount,
    required String currency,
    required String userEmail,
  }) async {
    // Validate context before processing
    if (!context.mounted) {
      throw const PaymentException(
        'Payment context is no longer valid during provider processing',
        code: 'PAYMENT_CONTEXT_INVALID',
      );
    }
    switch (config.provider) {
      case PaymentProvider.stripe:
        final result = await _stripeProvider.processPayment(
          context: context,
          config: config,
          transactionReference: transactionReference,
        );
        return ProviderPaymentResult(
          success: result.success,
          providerTransactionId: result.paymentIntentId,
          error: result.errorMessage,
        );

      case PaymentProvider.paystack:
        final result = await _paystackProvider.processCardPayment(
          context: context,
          config: config,
          transactionReference: transactionReference,
          amount: amount,
          currency: currency,
          userEmail: userEmail,
        );
        return ProviderPaymentResult(
          success: result.success,
          providerTransactionId: result.paystackReference,
          error: result.error,
        );

      case PaymentProvider.busha:
        final result = await _bushaProvider.processCryptoPayment(
          config: config,
          transactionReference: transactionReference,
          onStatusUpdate: (status) {
            // Handle real-time status updates
            _loggingService.info(
              'EnhancedPaymentService',
              'Crypto payment status update',
              {
                'transactionReference': transactionReference,
                'status': status.name,
              },
            );
          },
        );
        return ProviderPaymentResult(
          success: result.success,
          providerTransactionId: result.walletAddress,
          error: result.error,
        );
    }
  }

  /// Get user location for geolocation-aware payment routing
  Future<GeolocationData?> _getUserLocation() async {
    try {
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        final requestResult = await Geolocator.requestPermission();
        if (requestResult == LocationPermission.denied) {
          return null;
        }
      }

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.low,
        timeLimit: const Duration(seconds: 10),
      );

      // Convert coordinates to country/region using geocoding service
      final geocodingResult = await _performReverseGeocoding(
        position.latitude,
        position.longitude,
      );

      if (geocodingResult != null) {
        return GeolocationData(
          countryCode: geocodingResult.countryCode,
          countryName: geocodingResult.countryName,
          region: geocodingResult.region,
          city: geocodingResult.city,
          latitude: position.latitude,
          longitude: position.longitude,
          confidence: geocodingResult.confidence,
          isVpnDetected: geocodingResult.isVpnDetected,
          recommendedProvider:
              _getRecommendedProvider(geocodingResult.countryCode),
        );
      }

      // Fallback to default if geocoding fails
      return GeolocationData(
        countryCode: 'US',
        countryName: 'United States',
        region: 'North America',
        city: 'Unknown',
        latitude: position.latitude,
        longitude: position.longitude,
        confidence: 0.5,
        isVpnDetected: false,
        recommendedProvider: 'stripe',
      );
    } catch (e) {
      _loggingService.warning(
        'EnhancedPaymentService',
        'Failed to get user location',
        {'error': e.toString()},
      );
      return null;
    }
  }

  /// Perform reverse geocoding using multiple fallback strategies
  Future<GeocodingResult?> _performReverseGeocoding(
    double latitude,
    double longitude,
  ) async {
    try {
      // Strategy 1: Try backend geocoding service (when implemented)
      final backendResult = await _tryBackendGeocoding(latitude, longitude);
      if (backendResult != null) return backendResult;

      // Strategy 2: Use local country boundaries (offline fallback)
      final localResult = await _tryLocalGeocoding(latitude, longitude);
      if (localResult != null) return localResult;

      // Strategy 3: Use device locale as last resort
      return await _tryDeviceLocaleGeocoding();
    } catch (e) {
      _loggingService.warning(
        'EnhancedPaymentService',
        'Reverse geocoding failed',
        {'error': e.toString()},
      );
      return null;
    }
  }

  /// Try backend geocoding service (for future implementation)
  Future<GeocodingResult?> _tryBackendGeocoding(
    double latitude,
    double longitude,
  ) async {
    try {
      // TODO: Implement when backend geocoding service is available
      // final response = await _apiService.reverseGeocode(latitude, longitude);
      // return GeocodingResult.fromBackendResponse(response);
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Use local country boundaries for offline geocoding
  Future<GeocodingResult?> _tryLocalGeocoding(
    double latitude,
    double longitude,
  ) async {
    try {
      // Simple coordinate-based country detection for major regions
      final countryData = _getCountryFromCoordinates(latitude, longitude);

      if (countryData != null) {
        return GeocodingResult(
          countryCode: countryData['code']!,
          countryName: countryData['name']!,
          region: countryData['region']!,
          city: 'Unknown',
          confidence: 0.7,
          isVpnDetected: false,
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Simple coordinate-based country detection
  Map<String, String>? _getCountryFromCoordinates(double lat, double lng) {
    // Nigeria
    if (lat >= 4.0 && lat <= 14.0 && lng >= 2.0 && lng <= 15.0) {
      return {'code': 'NG', 'name': 'Nigeria', 'region': 'Africa'};
    }
    // Ghana
    if (lat >= 4.5 && lat <= 11.5 && lng >= -3.5 && lng <= 1.5) {
      return {'code': 'GH', 'name': 'Ghana', 'region': 'Africa'};
    }
    // Kenya
    if (lat >= -5.0 && lat <= 5.5 && lng >= 33.5 && lng <= 42.0) {
      return {'code': 'KE', 'name': 'Kenya', 'region': 'Africa'};
    }
    // South Africa
    if (lat >= -35.0 && lat <= -22.0 && lng >= 16.0 && lng <= 33.0) {
      return {'code': 'ZA', 'name': 'South Africa', 'region': 'Africa'};
    }
    // United States
    if (lat >= 24.0 && lat <= 49.0 && lng >= -125.0 && lng <= -66.0) {
      return {'code': 'US', 'name': 'United States', 'region': 'North America'};
    }
    // United Kingdom
    if (lat >= 49.5 && lat <= 61.0 && lng >= -8.0 && lng <= 2.0) {
      return {'code': 'GB', 'name': 'United Kingdom', 'region': 'Europe'};
    }

    return null;
  }

  /// Use device locale as fallback
  Future<GeocodingResult?> _tryDeviceLocaleGeocoding() async {
    try {
      // Use device locale to determine likely country
      final locale = Platform.localeName;
      final countryCode = locale.split('_').last.toUpperCase();

      // Map common country codes to regions
      final countryData = _getCountryDataFromCode(countryCode);

      if (countryData != null) {
        return GeocodingResult(
          countryCode: countryData['code']!,
          countryName: countryData['name']!,
          region: countryData['region']!,
          city: 'Unknown',
          confidence: 0.3,
          isVpnDetected: false,
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Get country data from country code
  Map<String, String>? _getCountryDataFromCode(String code) {
    const countryMap = {
      'NG': {'code': 'NG', 'name': 'Nigeria', 'region': 'Africa'},
      'GH': {'code': 'GH', 'name': 'Ghana', 'region': 'Africa'},
      'KE': {'code': 'KE', 'name': 'Kenya', 'region': 'Africa'},
      'ZA': {'code': 'ZA', 'name': 'South Africa', 'region': 'Africa'},
      'US': {'code': 'US', 'name': 'United States', 'region': 'North America'},
      'GB': {'code': 'GB', 'name': 'United Kingdom', 'region': 'Europe'},
      'CA': {'code': 'CA', 'name': 'Canada', 'region': 'North America'},
      'AU': {'code': 'AU', 'name': 'Australia', 'region': 'Oceania'},
    };

    return countryMap[code];
  }

  /// Get recommended payment provider based on country
  String _getRecommendedProvider(String countryCode) {
    const paystackCountries = {
      'NG',
      'GH',
      'KE',
      'ZA',
      'TZ',
      'UG',
      'RW',
      'CI',
      'SN',
      'BF'
    };

    return paystackCountries.contains(countryCode.toUpperCase())
        ? 'paystack'
        : 'stripe';
  }

  /// Get current payment initialization data
  PaymentInitResponse? get currentPaymentInit => _currentPaymentInit;

  /// Dispose resources
  void dispose() {
    _stripeProvider.dispose();
    _paystackProvider.dispose();
    _bushaProvider.dispose();
    _apiService.dispose();
    _isInitialized = false;
  }
}

/// Result model for enhanced payment operations
class EnhancedPaymentResult {
  final bool success;
  final String? transactionReference;
  final PaymentProvider? provider;
  final String? receiptId;
  final String? error;
  final Duration processingTime;

  const EnhancedPaymentResult({
    required this.success,
    this.transactionReference,
    this.provider,
    this.receiptId,
    this.error,
    required this.processingTime,
  });
}

/// Result model for geocoding operations
class GeocodingResult {
  final String countryCode;
  final String countryName;
  final String region;
  final String city;
  final double confidence;
  final bool isVpnDetected;

  const GeocodingResult({
    required this.countryCode,
    required this.countryName,
    required this.region,
    required this.city,
    required this.confidence,
    required this.isVpnDetected,
  });
}

/// Internal result model for provider operations
class ProviderPaymentResult {
  final bool success;
  final String? providerTransactionId;
  final String? error;

  const ProviderPaymentResult({
    required this.success,
    this.providerTransactionId,
    this.error,
  });
}
