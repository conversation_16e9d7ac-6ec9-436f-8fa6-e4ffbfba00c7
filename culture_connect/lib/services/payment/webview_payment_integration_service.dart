import 'dart:async';
import 'package:flutter/material.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/payment_providers/modern_paystack_provider.dart';
import 'package:culture_connect/services/payment_providers/real_stripe_provider.dart';
import 'package:culture_connect/services/payment_providers/busha_payment_provider.dart';
import 'package:culture_connect/services/payment_api_service.dart';
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/services/mascot_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Comprehensive WebView payment integration service
/// 
/// This service orchestrates the complete payment flow with WebView integration,
/// maintaining our backend-first architecture and zero technical debt standards
class WebViewPaymentIntegrationService {
  final ModernPaystackProvider _paystackProvider;
  final RealStripeProvider _stripeProvider;
  final BushaPaymentProvider _bushaProvider;
  final PaymentApiService _apiService;
  final AchievementService _achievementService;
  final MascotService _mascotService;
  final AnalyticsService _analyticsService;
  final LoggingService _loggingService;

  bool _isInitialized = false;

  WebViewPaymentIntegrationService({
    required ModernPaystackProvider paystackProvider,
    required RealStripeProvider stripeProvider,
    required BushaPaymentProvider bushaProvider,
    required PaymentApiService apiService,
    required AchievementService achievementService,
    required MascotService mascotService,
    required AnalyticsService analyticsService,
    required LoggingService loggingService,
  })  : _paystackProvider = paystackProvider,
        _stripeProvider = stripeProvider,
        _bushaProvider = bushaProvider,
        _apiService = apiService,
        _achievementService = achievementService,
        _mascotService = mascotService,
        _analyticsService = analyticsService,
        _loggingService = loggingService;

  /// Initialize the integration service
  Future<void> initialize({
    required String authToken,
  }) async {
    try {
      _loggingService.info(
        'WebViewPaymentIntegrationService',
        'Initializing payment integration service',
        {'timestamp': DateTime.now().toIso8601String()},
      );

      // Set authentication token
      _apiService.setAuthToken(authToken);

      // Initialize payment providers
      await _paystackProvider.initialize(
        publicKey: 'pk_test_...', // TODO: Get from backend
      );

      await _stripeProvider.initialize(
        publishableKey: 'pk_test_...', // TODO: Get from backend
        merchantDisplayName: 'CultureConnect',
      );

      await _bushaProvider.initialize();

      _isInitialized = true;

      _loggingService.info(
        'WebViewPaymentIntegrationService',
        'Payment integration service initialized successfully',
        {'providers': ['paystack', 'stripe', 'busha']},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'WebViewPaymentIntegrationService',
        'Failed to initialize payment integration service',
        {'error': e.toString()},
        stackTrace,
      );
      throw PaymentException(
        'Failed to initialize payment service: $e',
        code: 'PAYMENT_INTEGRATION_INIT_FAILED',
      );
    }
  }

  /// Process payment with intelligent provider routing and WebView integration
  Future<PaymentResult> processPayment({
    required BuildContext context,
    required Booking booking,
    required String userEmail,
    required String userName,
    required String userPhone,
    required GeolocationData? geolocationData,
  }) async {
    if (!_isInitialized) {
      throw const PaymentException(
        'Payment integration service not initialized',
        code: 'PAYMENT_INTEGRATION_NOT_INITIALIZED',
      );
    }

    if (!context.mounted) {
      throw const PaymentException(
        'Payment context is no longer valid',
        code: 'PAYMENT_CONTEXT_INVALID',
      );
    }

    try {
      _loggingService.info(
        'WebViewPaymentIntegrationService',
        'Starting payment processing',
        {
          'bookingId': booking.id,
          'amount': booking.totalAmount,
          'userEmail': userEmail,
          'geolocation': geolocationData?.toJson(),
        },
      );

      // Step 1: Initialize payment with backend
      final initRequest = PaymentInitRequest(
        bookingId: booking.id,
        amount: booking.totalAmount,
        currency: _determineCurrency(geolocationData),
        userEmail: userEmail,
        userName: userName,
        userPhone: userPhone,
        geolocationData: geolocationData,
      );

      final initResponse = await _apiService.initializePayment(initRequest);

      // Step 2: Determine optimal payment provider
      final provider = _determinePaymentProvider(geolocationData);

      // Step 3: Process payment with selected provider
      final providerResult = await _processWithProvider(
        context: context,
        provider: provider,
        config: initResponse.providerConfig,
        transactionReference: initResponse.transactionReference,
        amount: booking.totalAmount,
        currency: initRequest.currency,
        userEmail: userEmail,
      );

      // Step 4: Verify payment with backend
      if (providerResult.success) {
        final verificationResult = await _verifyPayment(
          initResponse.transactionReference,
        );

        if (verificationResult.isSuccess) {
          // Step 5: Trigger celebrations and analytics
          await _handlePaymentSuccess(
            booking: booking,
            transactionReference: initResponse.transactionReference,
            amount: booking.totalAmount,
            provider: provider,
          );

          return PaymentResult.success(
            transactionReference: initResponse.transactionReference,
            amount: booking.totalAmount,
            currency: initRequest.currency,
            provider: provider,
            receiptUrl: verificationResult.receiptUrl,
          );
        }
      }

      return PaymentResult.failure(
        error: providerResult.error ?? 'Payment verification failed',
        transactionReference: initResponse.transactionReference,
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'WebViewPaymentIntegrationService',
        'Payment processing failed',
        {
          'bookingId': booking.id,
          'error': e.toString(),
        },
        stackTrace,
      );

      return PaymentResult.failure(
        error: 'Payment failed: $e',
        transactionReference: null,
      );
    }
  }

  /// Determine optimal payment provider based on geolocation
  PaymentProvider _determinePaymentProvider(GeolocationData? geolocationData) {
    if (geolocationData == null) {
      return PaymentProvider.stripe; // Default fallback
    }

    // Paystack countries (Africa)
    const paystackCountries = {
      'NG', 'GH', 'KE', 'ZA', 'TZ', 'UG', 'RW', 'CI', 'SN', 'BF'
    };

    if (paystackCountries.contains(geolocationData.countryCode.toUpperCase())) {
      return PaymentProvider.paystack;
    }

    // Default to Stripe for global coverage
    return PaymentProvider.stripe;
  }

  /// Determine currency based on geolocation
  String _determineCurrency(GeolocationData? geolocationData) {
    if (geolocationData == null) {
      return 'USD'; // Default fallback
    }

    // Currency mapping based on country
    const currencyMap = {
      'NG': 'NGN',
      'GH': 'GHS',
      'KE': 'KES',
      'ZA': 'ZAR',
      'US': 'USD',
      'GB': 'GBP',
      'EU': 'EUR',
    };

    return currencyMap[geolocationData.countryCode.toUpperCase()] ?? 'USD';
  }

  /// Process payment with selected provider
  Future<ProviderPaymentResult> _processWithProvider({
    required BuildContext context,
    required PaymentProvider provider,
    required PaymentProviderConfig config,
    required String transactionReference,
    required double amount,
    required String currency,
    required String userEmail,
  }) async {
    switch (provider) {
      case PaymentProvider.paystack:
        final result = await _paystackProvider.processCardPayment(
          context: context,
          config: config,
          transactionReference: transactionReference,
          amount: amount,
          currency: currency,
          userEmail: userEmail,
        );
        return ProviderPaymentResult(
          success: result.success,
          transactionReference: result.transactionReference,
          providerReference: result.accessCode,
          message: result.message,
          error: result.error,
        );

      case PaymentProvider.stripe:
        // Stripe implementation (existing)
        return ProviderPaymentResult(
          success: false,
          transactionReference: transactionReference,
          error: 'Stripe implementation pending',
        );

      case PaymentProvider.busha:
        // Busha implementation (existing)
        return ProviderPaymentResult(
          success: false,
          transactionReference: transactionReference,
          error: 'Busha implementation pending',
        );
    }
  }

  /// Verify payment with backend
  Future<PaymentVerificationResponse> _verifyPayment(
    String transactionReference,
  ) async {
    final verificationRequest = PaymentVerificationRequest(
      transactionReference: transactionReference,
      timestamp: DateTime.now(),
    );

    return await _apiService.verifyPayment(verificationRequest);
  }

  /// Handle successful payment completion
  Future<void> _handlePaymentSuccess({
    required Booking booking,
    required String transactionReference,
    required double amount,
    required PaymentProvider provider,
  }) async {
    try {
      // Trigger achievement
      await _achievementService.unlockAchievement('payment_completed');

      // Show mascot celebration
      await _mascotService.showCelebration('payment_success');

      // Track analytics
      await _analyticsService.trackEvent('payment_completed', {
        'booking_id': booking.id,
        'amount': amount,
        'provider': provider.name,
        'transaction_reference': transactionReference,
      });

      _loggingService.info(
        'WebViewPaymentIntegrationService',
        'Payment success handling completed',
        {
          'transactionReference': transactionReference,
          'amount': amount,
          'provider': provider.name,
        },
      );
    } catch (e) {
      _loggingService.warning(
        'WebViewPaymentIntegrationService',
        'Failed to handle payment success celebrations',
        {'error': e.toString()},
      );
      // Don't throw - payment was successful, celebrations are optional
    }
  }

  /// Dispose resources
  void dispose() {
    _paystackProvider.dispose();
    _stripeProvider.dispose();
    _bushaProvider.dispose();
    _apiService.dispose();
    _isInitialized = false;
  }
}

/// Payment result model
class PaymentResult {
  final bool isSuccess;
  final String? transactionReference;
  final double? amount;
  final String? currency;
  final PaymentProvider? provider;
  final String? receiptUrl;
  final String? error;

  const PaymentResult._({
    required this.isSuccess,
    this.transactionReference,
    this.amount,
    this.currency,
    this.provider,
    this.receiptUrl,
    this.error,
  });

  factory PaymentResult.success({
    required String transactionReference,
    required double amount,
    required String currency,
    required PaymentProvider provider,
    String? receiptUrl,
  }) {
    return PaymentResult._(
      isSuccess: true,
      transactionReference: transactionReference,
      amount: amount,
      currency: currency,
      provider: provider,
      receiptUrl: receiptUrl,
    );
  }

  factory PaymentResult.failure({
    required String error,
    String? transactionReference,
  }) {
    return PaymentResult._(
      isSuccess: false,
      error: error,
      transactionReference: transactionReference,
    );
  }
}
