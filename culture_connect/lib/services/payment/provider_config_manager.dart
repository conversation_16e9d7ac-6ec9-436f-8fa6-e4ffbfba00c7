import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/payment/payment_config_service.dart';
import 'package:culture_connect/services/payment/real_payment_api_service.dart';
import 'package:culture_connect/services/payment_providers/real_stripe_provider.dart';
import 'package:culture_connect/services/payment_providers/paystack_payment_provider.dart';
import 'package:culture_connect/services/payment_providers/busha_payment_provider.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Manager for dynamic payment provider configuration and initialization
class ProviderConfigManager {
  static const Duration _configRefreshInterval = Duration(hours: 6);
  static const Duration _configRetryDelay = Duration(minutes: 5);

  final LoggingService _loggingService;
  final PaymentConfigService _configService;
  final RealPaymentApiService _apiService;
  final RealStripeProvider _stripeProvider;
  final PaystackPaymentProvider _paystackProvider;
  final BushaPaymentProvider _bushaProvider;

  bool _isInitialized = false;
  Timer? _configRefreshTimer;
  Timer? _configRetryTimer;
  Map<PaymentProvider, bool> _providerInitializationStatus = {};

  ProviderConfigManager({
    required LoggingService loggingService,
    required PaymentConfigService configService,
    required RealPaymentApiService apiService,
    required RealStripeProvider stripeProvider,
    required PaystackPaymentProvider paystackProvider,
    required BushaPaymentProvider bushaProvider,
  })  : _loggingService = loggingService,
        _configService = configService,
        _apiService = apiService,
        _stripeProvider = stripeProvider,
        _paystackProvider = paystackProvider,
        _bushaProvider = bushaProvider;

  /// Initialize the provider configuration manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load and apply provider configurations
      await _loadAndApplyConfigurations();

      // Setup automatic configuration refresh
      _setupConfigRefreshTimer();

      _isInitialized = true;

      _loggingService.info(
        'ProviderConfigManager',
        'Provider configuration manager initialized',
        {
          'initialized_providers': _providerInitializationStatus.entries
              .where((entry) => entry.value)
              .map((entry) => entry.key.name)
              .toList(),
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'ProviderConfigManager',
        'Failed to initialize provider configuration manager',
        {'error': e.toString()},
        stackTrace,
      );

      // Setup retry timer for failed initialization
      _setupConfigRetryTimer();
      rethrow;
    }
  }

  /// Get initialization status for all providers
  Map<PaymentProvider, bool> get providerInitializationStatus =>
      Map.unmodifiable(_providerInitializationStatus);

  /// Check if a specific provider is initialized
  bool isProviderInitialized(PaymentProvider provider) {
    return _providerInitializationStatus[provider] ?? false;
  }

  /// Get list of available (initialized) providers
  List<PaymentProvider> get availableProviders {
    return _providerInitializationStatus.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();
  }

  /// Force refresh provider configurations from backend
  Future<void> refreshConfigurations() async {
    if (!_isInitialized) {
      throw PaymentException(
        'Provider configuration manager not initialized',
        code: 'CONFIG_MANAGER_NOT_INITIALIZED',
      );
    }

    try {
      await _loadAndApplyConfigurations();

      _loggingService.info(
        'ProviderConfigManager',
        'Provider configurations refreshed successfully',
        {
          'available_providers': availableProviders.map((p) => p.name).toList(),
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'ProviderConfigManager',
        'Failed to refresh provider configurations',
        {'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Load configurations from backend and apply to providers
  Future<void> _loadAndApplyConfigurations() async {
    try {
      // Check if we need to refresh configurations
      if (!_configService.needsConfigRefresh) {
        await _initializeProvidersFromCache();
        return;
      }

      // Fetch fresh configurations from backend
      final configs = await _apiService.getProviderConfigurations();

      // Update config service with fresh data
      await _configService.updateProviderConfigurations(configs);

      // Initialize providers with new configurations
      await _initializeProviders(configs);
    } catch (e) {
      _loggingService.warning(
        'ProviderConfigManager',
        'Failed to load fresh configurations, trying cached configs',
        {'error': e.toString()},
      );

      // Fallback to cached configurations
      await _initializeProvidersFromCache();
    }
  }

  /// Initialize providers from cached configurations
  Future<void> _initializeProvidersFromCache() async {
    final cachedConfigs = _configService.getAllProviderConfigs();
    if (cachedConfigs.isNotEmpty) {
      await _initializeProviders(cachedConfigs);
    } else {
      _loggingService.warning(
        'ProviderConfigManager',
        'No cached configurations available, using fallback configs',
      );
      await _initializeProvidersWithFallback();
    }
  }

  /// Initialize payment providers with configurations
  Future<void> _initializeProviders(
    Map<PaymentProvider, PaymentProviderConfiguration> configs,
  ) async {
    _providerInitializationStatus.clear();

    // Initialize Stripe
    await _initializeStripe(configs[PaymentProvider.stripe]);

    // Initialize Paystack
    await _initializePaystack(configs[PaymentProvider.paystack]);

    // Initialize Busha
    await _initializeBusha(configs[PaymentProvider.busha]);

    _loggingService.info(
      'ProviderConfigManager',
      'Provider initialization completed',
      {
        'successful_providers': _providerInitializationStatus.entries
            .where((entry) => entry.value)
            .map((entry) => entry.key.name)
            .toList(),
        'failed_providers': _providerInitializationStatus.entries
            .where((entry) => !entry.value)
            .map((entry) => entry.key.name)
            .toList(),
      },
    );
  }

  /// Initialize Stripe provider
  Future<void> _initializeStripe(PaymentProviderConfiguration? config) async {
    try {
      if (config == null || !config.isEnabled) {
        _providerInitializationStatus[PaymentProvider.stripe] = false;
        _loggingService.info(
          'ProviderConfigManager',
          'Stripe provider disabled or not configured',
        );
        return;
      }

      await _stripeProvider.initialize(
        publishableKey: config.publicKey,
        merchantDisplayName: config.merchantDisplayName ?? 'CultureConnect',
      );

      _providerInitializationStatus[PaymentProvider.stripe] = true;

      _loggingService.info(
        'ProviderConfigManager',
        'Stripe provider initialized successfully',
        {'merchant_name': config.merchantDisplayName},
      );
    } catch (e, stackTrace) {
      _providerInitializationStatus[PaymentProvider.stripe] = false;

      _loggingService.error(
        'ProviderConfigManager',
        'Failed to initialize Stripe provider',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Initialize Paystack provider
  Future<void> _initializePaystack(PaymentProviderConfiguration? config) async {
    try {
      if (config == null || !config.isEnabled) {
        _providerInitializationStatus[PaymentProvider.paystack] = false;
        _loggingService.info(
          'ProviderConfigManager',
          'Paystack provider disabled or not configured',
        );
        return;
      }

      await _paystackProvider.initialize(
        publicKey: config.publicKey,
      );

      _providerInitializationStatus[PaymentProvider.paystack] = true;

      _loggingService.info(
        'ProviderConfigManager',
        'Paystack provider initialized successfully',
      );
    } catch (e, stackTrace) {
      _providerInitializationStatus[PaymentProvider.paystack] = false;

      _loggingService.error(
        'ProviderConfigManager',
        'Failed to initialize Paystack provider',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Initialize Busha provider
  Future<void> _initializeBusha(PaymentProviderConfiguration? config) async {
    try {
      if (config == null || !config.isEnabled) {
        _providerInitializationStatus[PaymentProvider.busha] = false;
        _loggingService.info(
          'ProviderConfigManager',
          'Busha provider disabled or not configured',
        );
        return;
      }

      await _bushaProvider.initialize();

      _providerInitializationStatus[PaymentProvider.busha] = true;

      _loggingService.info(
        'ProviderConfigManager',
        'Busha provider initialized successfully',
      );
    } catch (e, stackTrace) {
      _providerInitializationStatus[PaymentProvider.busha] = false;

      _loggingService.error(
        'ProviderConfigManager',
        'Failed to initialize Busha provider',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Initialize providers with fallback configurations (for development/testing)
  Future<void> _initializeProvidersWithFallback() async {
    _loggingService.warning(
      'ProviderConfigManager',
      'Using fallback provider configurations',
    );

    // Only initialize providers in development mode with test keys
    if (kDebugMode) {
      try {
        await _stripeProvider.initialize(
          publishableKey: 'pk_test_fallback_key',
          merchantDisplayName: 'CultureConnect Dev',
        );
        _providerInitializationStatus[PaymentProvider.stripe] = true;
      } catch (e) {
        _providerInitializationStatus[PaymentProvider.stripe] = false;
      }

      try {
        await _paystackProvider.initialize(
          publicKey: 'pk_test_fallback_key',
        );
        _providerInitializationStatus[PaymentProvider.paystack] = true;
      } catch (e) {
        _providerInitializationStatus[PaymentProvider.paystack] = false;
      }

      try {
        await _bushaProvider.initialize();
        _providerInitializationStatus[PaymentProvider.busha] = true;
      } catch (e) {
        _providerInitializationStatus[PaymentProvider.busha] = false;
      }
    } else {
      // In production, don't initialize without proper configs
      _providerInitializationStatus = {
        PaymentProvider.stripe: false,
        PaymentProvider.paystack: false,
        PaymentProvider.busha: false,
      };
    }
  }

  /// Setup automatic configuration refresh timer
  void _setupConfigRefreshTimer() {
    _configRefreshTimer?.cancel();
    _configRefreshTimer = Timer.periodic(_configRefreshInterval, (_) async {
      try {
        await _loadAndApplyConfigurations();
      } catch (e) {
        _loggingService.warning(
          'ProviderConfigManager',
          'Automatic configuration refresh failed',
          {'error': e.toString()},
        );
      }
    });
  }

  /// Setup configuration retry timer for failed initialization
  void _setupConfigRetryTimer() {
    _configRetryTimer?.cancel();
    _configRetryTimer = Timer(_configRetryDelay, () async {
      try {
        await _loadAndApplyConfigurations();
        _configRetryTimer?.cancel();
      } catch (e) {
        _loggingService.warning(
          'ProviderConfigManager',
          'Configuration retry failed, will try again later',
          {'error': e.toString()},
        );
        _setupConfigRetryTimer(); // Schedule another retry
      }
    });
  }

  /// Dispose resources
  void dispose() {
    _configRefreshTimer?.cancel();
    _configRetryTimer?.cancel();
    _isInitialized = false;
    _providerInitializationStatus.clear();
  }
}
