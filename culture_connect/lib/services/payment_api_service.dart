import 'dart:async';
import 'package:dio/dio.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Production-ready payment API service with backend integration
class PaymentApiService {
  static const int _timeoutSeconds = 30;
  static const int _maxRetries = 3;
  static const String _baseUrl =
      'https://api.cultureconnect.com'; // TODO: Get from environment config

  final Dio _dio;
  final LoggingService _loggingService;
  String? _authToken;

  PaymentApiService({
    required LoggingService loggingService,
  })  : _loggingService = loggingService,
        _dio = Dio(BaseOptions(
          baseUrl: _baseUrl,
          connectTimeout: const Duration(seconds: _timeoutSeconds),
          receiveTimeout: const Duration(seconds: _timeoutSeconds),
          sendTimeout: const Duration(seconds: _timeoutSeconds),
          headers: const {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        )) {
    _setupInterceptors();
  }

  /// Set authentication token for API requests
  void setAuthToken(String token) {
    _authToken = token;
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  /// Setup Dio interceptors for logging and error handling
  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          final correlationId = _generateCorrelationId();
          options.headers['X-Correlation-ID'] = correlationId;

          _loggingService.info(
            'PaymentApiService',
            'API Request: ${options.method} ${options.path}',
            {
              'correlationId': correlationId,
              'headers': options.headers,
            },
          );

          handler.next(options);
        },
        onResponse: (response, handler) {
          _loggingService.info(
            'PaymentApiService',
            'API Response: ${response.statusCode}',
            {
              'correlationId':
                  response.requestOptions.headers['X-Correlation-ID'],
              'statusCode': response.statusCode,
              'responseTime': '${DateTime.now().millisecondsSinceEpoch}ms',
            },
          );

          handler.next(response);
        },
        onError: (error, handler) {
          _loggingService.error(
            'PaymentApiService',
            'API Error: ${error.message}',
            {
              'correlationId': error.requestOptions.headers['X-Correlation-ID'],
              'statusCode': error.response?.statusCode,
              'error': error.toString(),
            },
            error.stackTrace,
          );

          handler.next(error);
        },
      ),
    );
  }

  /// Initialize payment with backend
  ///
  /// TODO: Backend Integration Required
  /// - Endpoint: POST /api/payments/initialize
  /// - Authentication: Bearer JWT token
  /// - Request format: PaymentInitRequest.toJson()
  /// - Response format: PaymentInitResponse.fromJson()
  /// - Error handling: 400 (validation), 401 (auth), 500 (server)
  Future<PaymentInitResponse> initializePayment(
    PaymentInitRequest request,
  ) async {
    try {
      final response = await _retryRequest(() async {
        return await _dio.post(
          '/api/payments/initialize',
          data: request.toJson(),
        );
      });

      if (response.statusCode == 200) {
        return PaymentInitResponse.fromJson(
          response.data as Map<String, dynamic>,
        );
      } else {
        throw PaymentApiException(
          'Payment initialization failed',
          statusCode: response.statusCode,
          correlationId: response.requestOptions.headers['X-Correlation-ID'],
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw PaymentApiException(
        'Unexpected error during payment initialization: $e',
        correlationId: _generateCorrelationId(),
      );
    }
  }

  /// Verify payment status with backend
  ///
  /// TODO: Backend Integration Required
  /// - Endpoint: POST /api/payments/verify
  /// - Authentication: Bearer JWT token
  /// - Request format: PaymentVerificationRequest.toJson()
  /// - Response format: PaymentVerificationResponse.fromJson()
  /// - Polling interval: 5 seconds for pending payments
  Future<PaymentVerificationResponse> verifyPayment(
    PaymentVerificationRequest request,
  ) async {
    try {
      final response = await _retryRequest(() async {
        return await _dio.post(
          '/api/payments/verify',
          data: request.toJson(),
        );
      });

      if (response.statusCode == 200) {
        return PaymentVerificationResponse.fromJson(
          response.data as Map<String, dynamic>,
        );
      } else {
        throw PaymentApiException(
          'Payment verification failed',
          statusCode: response.statusCode,
          correlationId: response.requestOptions.headers['X-Correlation-ID'],
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw PaymentApiException(
        'Unexpected error during payment verification: $e',
        correlationId: _generateCorrelationId(),
      );
    }
  }

  /// Get payment status by transaction reference
  ///
  /// TODO: Backend Integration Required
  /// - Endpoint: GET /api/payments/status/{reference}
  /// - Authentication: Bearer JWT token
  /// - Response format: PaymentVerificationResponse.fromJson()
  /// - Used for real-time status monitoring
  Future<PaymentVerificationResponse> getPaymentStatus(
    String transactionReference,
  ) async {
    try {
      final response = await _retryRequest(() async {
        return await _dio.get(
          '/api/payments/status/$transactionReference',
        );
      });

      if (response.statusCode == 200) {
        return PaymentVerificationResponse.fromJson(
          response.data as Map<String, dynamic>,
        );
      } else {
        throw PaymentApiException(
          'Failed to get payment status',
          statusCode: response.statusCode,
          correlationId: response.requestOptions.headers['X-Correlation-ID'],
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw PaymentApiException(
        'Unexpected error getting payment status: $e',
        correlationId: _generateCorrelationId(),
      );
    }
  }

  /// Download payment receipt PDF
  ///
  /// TODO: Backend Integration Required
  /// - Endpoint: GET /api/payments/receipt/{receiptId}
  /// - Authentication: Bearer JWT token
  /// - Response: PDF file bytes
  /// - Content-Type: application/pdf
  Future<List<int>> downloadReceipt(String receiptId) async {
    try {
      final response = await _retryRequest(() async {
        return await _dio.get(
          '/api/payments/receipt/$receiptId',
          options: Options(responseType: ResponseType.bytes),
        );
      });

      if (response.statusCode == 200) {
        return response.data as List<int>;
      } else {
        throw PaymentApiException(
          'Failed to download receipt',
          statusCode: response.statusCode,
          correlationId: response.requestOptions.headers['X-Correlation-ID'],
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw PaymentApiException(
        'Unexpected error downloading receipt: $e',
        correlationId: _generateCorrelationId(),
      );
    }
  }

  /// Retry request with exponential backoff
  Future<Response> _retryRequest(Future<Response> Function() request) async {
    int attempts = 0;
    Duration delay = const Duration(milliseconds: 500);

    while (attempts < _maxRetries) {
      try {
        return await request();
      } on DioException catch (e) {
        attempts++;

        if (attempts >= _maxRetries || !_shouldRetry(e)) {
          rethrow;
        }

        _loggingService.warning(
          'PaymentApiService',
          'Request failed, retrying in ${delay.inMilliseconds}ms (attempt $attempts/$_maxRetries)',
          {'error': e.toString()},
        );

        await Future.delayed(delay);
        delay *= 2; // Exponential backoff
      }
    }

    throw PaymentApiException(
      'Max retry attempts exceeded',
      correlationId: _generateCorrelationId(),
    );
  }

  /// Check if error should trigger a retry
  bool _shouldRetry(DioException error) {
    if (error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout) {
      return true;
    }

    if (error.response?.statusCode != null) {
      final statusCode = error.response!.statusCode!;
      return statusCode >= 500 ||
          statusCode == 429; // Server errors or rate limiting
    }

    return false;
  }

  /// Handle Dio exceptions and convert to PaymentApiException
  PaymentApiException _handleDioException(DioException error) {
    final correlationId =
        error.requestOptions.headers['X-Correlation-ID'] as String?;

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return PaymentApiException(
          'Connection timeout. Please check your internet connection.',
          statusCode: null,
          correlationId: correlationId,
          isNetworkError: true,
        );

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?['message'] as String? ??
            'Request failed with status $statusCode';

        return PaymentApiException(
          message,
          statusCode: statusCode,
          correlationId: correlationId,
        );

      case DioExceptionType.cancel:
        return PaymentApiException(
          'Request was cancelled',
          correlationId: correlationId,
        );

      default:
        return PaymentApiException(
          'Network error: ${error.message}',
          correlationId: correlationId,
          isNetworkError: true,
        );
    }
  }

  /// Generate unique correlation ID for request tracking
  String _generateCorrelationId() {
    return 'cc_${DateTime.now().millisecondsSinceEpoch}_${_generateRandomString(8)}';
  }

  /// Generate random string for correlation ID
  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    return List.generate(
        length,
        (index) => chars[(DateTime.now().millisecondsSinceEpoch + index) %
            chars.length]).join();
  }

  /// Dispose resources
  void dispose() {
    _dio.close();
  }
}
