import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Comprehensive error handling for payment operations
class PaymentErrorHandler {
  final LoggingService _loggingService;
  static const int _maxRetryAttempts = 3;
  static const Duration _baseRetryDelay = Duration(seconds: 2);

  PaymentErrorHandler({
    required LoggingService loggingService,
  }) : _loggingService = loggingService;

  /// Handle payment errors with user-friendly messages and retry logic
  Future<PaymentErrorResult> handleError({
    required Exception error,
    required String operation,
    required BuildContext context,
    String? transactionReference,
    int retryAttempt = 0,
    Future<void> Function()? retryCallback,
  }) async {
    _loggingService.error(
      'PaymentErrorHandler',
      'Payment error occurred',
      {
        'operation': operation,
        'error': error.toString(),
        'transactionReference': transactionReference,
        'retryAttempt': retryAttempt,
      },
    );

    // Determine error type and appropriate response
    final errorInfo = _analyzeError(error);

    // Check if retry is appropriate
    if (errorInfo.isRetryable &&
        retryAttempt < _maxRetryAttempts &&
        retryCallback != null) {
      return _handleRetryableError(
        errorInfo: errorInfo,
        operation: operation,
        context: context,
        transactionReference: transactionReference,
        retryAttempt: retryAttempt,
        retryCallback: retryCallback,
      );
    } else {
      return _handleFinalError(
        errorInfo: errorInfo,
        operation: operation,
        context: context,
        transactionReference: transactionReference,
      );
    }
  }

  /// Handle retryable errors with exponential backoff
  Future<PaymentErrorResult> _handleRetryableError({
    required PaymentErrorInfo errorInfo,
    required String operation,
    required BuildContext context,
    String? transactionReference,
    required int retryAttempt,
    required Future<void> Function() retryCallback,
  }) async {
    final delay = _calculateRetryDelay(retryAttempt);

    _loggingService.info(
      'PaymentErrorHandler',
      'Retrying payment operation',
      {
        'operation': operation,
        'retryAttempt': retryAttempt + 1,
        'delayMs': delay.inMilliseconds,
        'transactionReference': transactionReference,
      },
    );

    // Show retry notification to user
    if (context.mounted) {
      _showRetryNotification(context, errorInfo, retryAttempt + 1);
    }

    // Wait before retry
    await Future.delayed(delay);

    try {
      // Attempt retry
      await retryCallback();

      _loggingService.info(
        'PaymentErrorHandler',
        'Payment operation retry successful',
        {
          'operation': operation,
          'retryAttempt': retryAttempt + 1,
          'transactionReference': transactionReference,
        },
      );

      return const PaymentErrorResult(
        isResolved: true,
        userMessage: 'Operation completed successfully after retry',
        shouldRetry: false,
      );
    } catch (retryError) {
      // Retry failed, handle recursively
      return handleError(
        error: retryError as Exception,
        operation: operation,
        context: context,
        transactionReference: transactionReference,
        retryAttempt: retryAttempt + 1,
        retryCallback: retryCallback,
      );
    }
  }

  /// Handle final errors that cannot be retried
  Future<PaymentErrorResult> _handleFinalError({
    required PaymentErrorInfo errorInfo,
    required String operation,
    required BuildContext context,
    String? transactionReference,
  }) async {
    _loggingService.warning(
      'PaymentErrorHandler',
      'Payment operation failed permanently',
      {
        'operation': operation,
        'errorType': errorInfo.type.name,
        'transactionReference': transactionReference,
      },
    );

    // Show error dialog to user
    if (context.mounted) {
      await _showErrorDialog(context, errorInfo);
    }

    return PaymentErrorResult(
      isResolved: false,
      userMessage: errorInfo.userMessage,
      shouldRetry: false,
      fallbackOptions: _getFallbackOptions(errorInfo),
    );
  }

  /// Analyze error and determine appropriate handling
  PaymentErrorInfo _analyzeError(Exception error) {
    if (error is PaymentApiException) {
      return _analyzeApiError(error);
    } else if (error is PaymentProviderException) {
      return _analyzeProviderError(error);
    } else if (error is PaymentValidationException) {
      return _analyzeValidationError(error);
    } else if (error is PaymentTimeoutException) {
      return _analyzeTimeoutError(error);
    } else {
      return PaymentErrorInfo(
        type: PaymentErrorType.unknown,
        userMessage: 'An unexpected error occurred. Please try again.',
        technicalMessage: error.toString(),
        isRetryable: true,
        severity: PaymentErrorSeverity.medium,
      );
    }
  }

  /// Analyze API-specific errors
  PaymentErrorInfo _analyzeApiError(PaymentApiException error) {
    if (error.isNetworkError) {
      return PaymentErrorInfo(
        type: PaymentErrorType.network,
        userMessage:
            'Network connection issue. Please check your internet connection and try again.',
        technicalMessage: error.toString(),
        isRetryable: true,
        severity: PaymentErrorSeverity.medium,
      );
    }

    switch (error.statusCode) {
      case 400:
        return PaymentErrorInfo(
          type: PaymentErrorType.validation,
          userMessage:
              'Invalid payment information. Please check your details and try again.',
          technicalMessage: error.toString(),
          isRetryable: false,
          severity: PaymentErrorSeverity.low,
        );

      case 401:
        return PaymentErrorInfo(
          type: PaymentErrorType.authentication,
          userMessage: 'Authentication failed. Please log in again.',
          technicalMessage: error.toString(),
          isRetryable: false,
          severity: PaymentErrorSeverity.high,
        );

      case 429:
        return PaymentErrorInfo(
          type: PaymentErrorType.rateLimited,
          userMessage: 'Too many requests. Please wait a moment and try again.',
          technicalMessage: error.toString(),
          isRetryable: true,
          severity: PaymentErrorSeverity.medium,
        );

      case 500:
      case 502:
      case 503:
        return PaymentErrorInfo(
          type: PaymentErrorType.serverError,
          userMessage:
              'Server temporarily unavailable. Please try again in a few moments.',
          technicalMessage: error.toString(),
          isRetryable: true,
          severity: PaymentErrorSeverity.high,
        );

      default:
        return PaymentErrorInfo(
          type: PaymentErrorType.api,
          userMessage:
              'Payment service error. Please try again or contact support.',
          technicalMessage: error.toString(),
          isRetryable: true,
          severity: PaymentErrorSeverity.medium,
        );
    }
  }

  /// Analyze payment provider errors
  PaymentErrorInfo _analyzeProviderError(PaymentProviderException error) {
    switch (error.provider.toLowerCase()) {
      case 'stripe':
        return _analyzeStripeError(error);
      case 'paystack':
        return _analyzePaystackError(error);
      case 'busha':
        return _analyzeBushaError(error);
      default:
        return PaymentErrorInfo(
          type: PaymentErrorType.provider,
          userMessage:
              'Payment provider error. Please try a different payment method.',
          technicalMessage: error.toString(),
          isRetryable: false,
          severity: PaymentErrorSeverity.medium,
        );
    }
  }

  /// Analyze Stripe-specific errors
  PaymentErrorInfo _analyzeStripeError(PaymentProviderException error) {
    if (error.providerCode?.contains('card_declined') == true) {
      return PaymentErrorInfo(
        type: PaymentErrorType.cardDeclined,
        userMessage:
            'Your card was declined. Please try a different payment method.',
        technicalMessage: error.toString(),
        isRetryable: false,
        severity: PaymentErrorSeverity.low,
      );
    }

    if (error.providerCode?.contains('insufficient_funds') == true) {
      return PaymentErrorInfo(
        type: PaymentErrorType.insufficientFunds,
        userMessage:
            'Insufficient funds. Please check your account balance or try a different card.',
        technicalMessage: error.toString(),
        isRetryable: false,
        severity: PaymentErrorSeverity.low,
      );
    }

    return PaymentErrorInfo(
      type: PaymentErrorType.provider,
      userMessage:
          'Card payment failed. Please try again or use a different payment method.',
      technicalMessage: error.toString(),
      isRetryable: true,
      severity: PaymentErrorSeverity.medium,
    );
  }

  /// Analyze Paystack-specific errors
  PaymentErrorInfo _analyzePaystackError(PaymentProviderException error) {
    return PaymentErrorInfo(
      type: PaymentErrorType.provider,
      userMessage:
          'Payment failed. Please try again or use a different payment method.',
      technicalMessage: error.toString(),
      isRetryable: true,
      severity: PaymentErrorSeverity.medium,
    );
  }

  /// Analyze Busha-specific errors
  PaymentErrorInfo _analyzeBushaError(PaymentProviderException error) {
    return PaymentErrorInfo(
      type: PaymentErrorType.provider,
      userMessage:
          'Cryptocurrency payment failed. Please check your wallet and try again.',
      technicalMessage: error.toString(),
      isRetryable: true,
      severity: PaymentErrorSeverity.medium,
    );
  }

  /// Analyze validation errors
  PaymentErrorInfo _analyzeValidationError(PaymentValidationException error) {
    return PaymentErrorInfo(
      type: PaymentErrorType.validation,
      userMessage: 'Please check your payment information and try again.',
      technicalMessage: error.toString(),
      isRetryable: false,
      severity: PaymentErrorSeverity.low,
      validationErrors: error.fieldErrors,
    );
  }

  /// Analyze timeout errors
  PaymentErrorInfo _analyzeTimeoutError(PaymentTimeoutException error) {
    return PaymentErrorInfo(
      type: PaymentErrorType.timeout,
      userMessage:
          'Payment timed out. Please check your connection and try again.',
      technicalMessage: error.toString(),
      isRetryable: true,
      severity: PaymentErrorSeverity.medium,
    );
  }

  /// Calculate retry delay with exponential backoff
  Duration _calculateRetryDelay(int retryAttempt) {
    final multiplier = pow(2, retryAttempt).toInt();
    final delayMs = _baseRetryDelay.inMilliseconds * multiplier;

    // Add jitter to prevent thundering herd
    final jitter = Random().nextInt(1000);

    return Duration(milliseconds: delayMs + jitter);
  }

  /// Show retry notification to user
  void _showRetryNotification(
    BuildContext context,
    PaymentErrorInfo errorInfo,
    int retryAttempt,
  ) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Retrying payment... (Attempt $retryAttempt/$_maxRetryAttempts)'),
        backgroundColor: AppTheme.warningColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Show error dialog to user
  Future<void> _showErrorDialog(
    BuildContext context,
    PaymentErrorInfo errorInfo,
  ) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                _getErrorIcon(errorInfo.type),
                color: _getErrorColor(errorInfo.severity),
              ),
              const SizedBox(width: 8),
              const Text('Payment Error'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(errorInfo.userMessage),
              if (errorInfo.validationErrors?.isNotEmpty == true) ...[
                const SizedBox(height: 16),
                const Text(
                  'Please fix the following issues:',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                ...errorInfo.validationErrors!.entries.map(
                  (entry) => Padding(
                    padding: const EdgeInsets.only(left: 16, bottom: 4),
                    child: Text('• ${entry.value.join(', ')}'),
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Get fallback options for different error types
  List<PaymentFallbackOption> _getFallbackOptions(PaymentErrorInfo errorInfo) {
    switch (errorInfo.type) {
      case PaymentErrorType.cardDeclined:
      case PaymentErrorType.insufficientFunds:
        return [
          PaymentFallbackOption.differentPaymentMethod,
          PaymentFallbackOption.contactSupport,
        ];

      case PaymentErrorType.network:
      case PaymentErrorType.timeout:
        return [
          PaymentFallbackOption.checkConnection,
          PaymentFallbackOption.tryAgainLater,
        ];

      case PaymentErrorType.authentication:
        return [
          PaymentFallbackOption.relogin,
          PaymentFallbackOption.contactSupport,
        ];

      default:
        return [
          PaymentFallbackOption.tryAgainLater,
          PaymentFallbackOption.contactSupport,
        ];
    }
  }

  /// Get appropriate icon for error type
  IconData _getErrorIcon(PaymentErrorType type) {
    switch (type) {
      case PaymentErrorType.network:
        return Icons.wifi_off;
      case PaymentErrorType.cardDeclined:
      case PaymentErrorType.insufficientFunds:
        return Icons.credit_card_off;
      case PaymentErrorType.authentication:
        return Icons.lock;
      case PaymentErrorType.timeout:
        return Icons.access_time;
      default:
        return Icons.error_outline;
    }
  }

  /// Get appropriate color for error severity
  Color _getErrorColor(PaymentErrorSeverity severity) {
    switch (severity) {
      case PaymentErrorSeverity.low:
        return AppTheme.warningColor;
      case PaymentErrorSeverity.medium:
        return AppTheme.errorColor;
      case PaymentErrorSeverity.high:
        return AppTheme.errorColor;
    }
  }
}

/// Error information with handling details
class PaymentErrorInfo {
  final PaymentErrorType type;
  final String userMessage;
  final String technicalMessage;
  final bool isRetryable;
  final PaymentErrorSeverity severity;
  final Map<String, List<String>>? validationErrors;

  const PaymentErrorInfo({
    required this.type,
    required this.userMessage,
    required this.technicalMessage,
    required this.isRetryable,
    required this.severity,
    this.validationErrors,
  });
}

/// Result of error handling
class PaymentErrorResult {
  final bool isResolved;
  final String userMessage;
  final bool shouldRetry;
  final List<PaymentFallbackOption>? fallbackOptions;

  const PaymentErrorResult({
    required this.isResolved,
    required this.userMessage,
    required this.shouldRetry,
    this.fallbackOptions,
  });
}

/// Types of payment errors
enum PaymentErrorType {
  network,
  api,
  provider,
  validation,
  authentication,
  timeout,
  cardDeclined,
  insufficientFunds,
  rateLimited,
  serverError,
  unknown,
}

/// Severity levels for payment errors
enum PaymentErrorSeverity {
  low,
  medium,
  high,
}

/// Fallback options for users
enum PaymentFallbackOption {
  tryAgainLater,
  differentPaymentMethod,
  checkConnection,
  relogin,
  contactSupport,
}
