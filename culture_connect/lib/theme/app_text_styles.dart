import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_colors.dart';

/// App text styles
class AppTextStyles {
  /// Headline 1 text style
  static TextStyle get headline1 {
    return const TextStyle(
      fontSize: 96,
      fontWeight: FontWeight.w300,
      letterSpacing: -1.5,
      color: AppColors.text,
    );
  }

  /// Headline 2 text style
  static TextStyle get headline2 {
    return const TextStyle(
      fontSize: 60,
      fontWeight: FontWeight.w300,
      letterSpacing: -0.5,
      color: AppColors.text,
    );
  }

  /// Headline 3 text style
  static TextStyle get headline3 {
    return const TextStyle(
      fontSize: 48,
      fontWeight: FontWeight.w400,
      color: AppColors.text,
    );
  }

  /// Headline 4 text style
  static TextStyle get headline4 {
    return const TextStyle(
      fontSize: 34,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
      color: AppColors.text,
    );
  }

  /// Headline 5 text style
  static TextStyle get headline5 {
    return const TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w400,
      color: AppColors.text,
    );
  }

  /// Headline 6 text style
  static TextStyle get headline6 {
    return const TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.15,
      color: AppColors.text,
    );
  }

  /// Subtitle 1 text style
  static TextStyle get subtitle1 {
    return const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.15,
      color: AppColors.text,
    );
  }

  /// Subtitle 2 text style
  static TextStyle get subtitle2 {
    return const TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      color: AppColors.text,
    );
  }

  /// Body 1 text style
  static TextStyle get body1 {
    return const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.5,
      color: AppColors.text,
    );
  }

  /// Body 2 text style
  static TextStyle get body2 {
    return const TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
      color: AppColors.text,
    );
  }

  /// Button text style
  static TextStyle get button {
    return const TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 1.25,
      color: AppColors.primary,
    );
  }

  /// Caption text style
  static TextStyle get caption {
    return const TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.4,
      color: AppColors.textSecondary,
    );
  }

  /// Overline text style
  static TextStyle get overline {
    return const TextStyle(
      fontSize: 10,
      fontWeight: FontWeight.w400,
      letterSpacing: 1.5,
      color: AppColors.textSecondary,
    );
  }
}
