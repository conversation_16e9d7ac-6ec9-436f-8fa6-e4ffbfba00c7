import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/cultural_context_model.dart';
import 'package:culture_connect/providers/cultural_context_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// A screen for cultural context settings
class CulturalContextSettingsScreen extends ConsumerWidget {
  /// Creates a new cultural context settings screen
  const CulturalContextSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final useCulturalContext = ref.watch(useCulturalContextProvider);
    final showSensitiveContent = ref.watch(showSensitiveContentProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Cultural Context Settings',
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            const Text(
              'Cultural Context Awareness',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            const SizedBox(height: 8),

            const Text(
              'Cultural context awareness helps you understand cultural nuances, idioms, and references in translations. This feature provides additional context to help you communicate more effectively across cultures.',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),

            const SizedBox(height: 24),

            // Main settings
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'General Settings',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Cultural context awareness toggle
                    SwitchListTile(
                      title: const Text(
                        'Enable Cultural Context',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: const Text(
                        'Show cultural notes and context for translations',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: useCulturalContext,
                      onChanged: (value) {
                        ref
                            .read(culturalContextNotifierProvider.notifier)
                            .setUseCulturalContext(value);
                      },
                      activeColor: AppTheme.primaryColor,
                    ),

                    // Show sensitive content toggle
                    SwitchListTile(
                      title: const Text(
                        'Show Sensitive Content',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: const Text(
                        'Display cultural notes about sensitive topics',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: showSensitiveContent,
                      onChanged: useCulturalContext
                          ? (value) {
                              ref
                                  .read(
                                      culturalContextNotifierProvider.notifier)
                                  .setShowSensitiveContent(value);
                            }
                          : null,
                      activeColor: AppTheme.primaryColor,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Context types
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Types of Cultural Context',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...CulturalContextType.values.map((type) {
                      return _buildContextTypeItem(type);
                    }).toList(),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Benefits
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Benefits of Cultural Context',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildBenefitItem(
                      Icons.emoji_objects_outlined,
                      'Better Understanding',
                      'Gain insights into cultural nuances and references',
                    ),
                    _buildBenefitItem(
                      Icons.error_outline,
                      'Avoid Misunderstandings',
                      'Prevent cultural faux pas and miscommunications',
                    ),
                    _buildBenefitItem(
                      Icons.school_outlined,
                      'Learn About Cultures',
                      'Expand your cultural knowledge while communicating',
                    ),
                    _buildBenefitItem(
                      Icons.people_outline,
                      'Build Better Relationships',
                      'Connect more deeply with people from different cultures',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Clear cache button
            Center(
              child: ElevatedButton.icon(
                onPressed: () {
                  ref
                      .read(culturalContextNotifierProvider.notifier)
                      .clearCache();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Cultural context cache cleared'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                icon: const Icon(Icons.delete_outline),
                label: const Text('Clear Cultural Context Cache'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a context type item
  Widget _buildContextTypeItem(CulturalContextType type) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: type.color.withAlpha(26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              type.icon,
              size: 24,
              color: type.color,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type.displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getContextTypeDescription(type),
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build a benefit item
  Widget _buildBenefitItem(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 24,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Get the description for a context type
  String _getContextTypeDescription(CulturalContextType type) {
    switch (type) {
      case CulturalContextType.general:
        return 'General cultural information relevant to the translation.';
      case CulturalContextType.idiom:
        return 'Expressions that cannot be understood from the individual meanings of their words.';
      case CulturalContextType.reference:
        return 'References to cultural elements, events, or figures that may not be familiar.';
      case CulturalContextType.taboo:
        return 'Content that may be considered sensitive or inappropriate in certain contexts.';
      case CulturalContextType.regional:
        return 'Usage specific to certain regions or dialects.';
      case CulturalContextType.slang:
        return 'Informal language or expressions used by particular groups.';
      case CulturalContextType.formality:
        return 'Information about formal vs. informal usage and social context.';
      case CulturalContextType.historical:
        return 'Historical context that helps understand the meaning or significance.';
      case CulturalContextType.religious:
        return 'References to religious concepts, practices, or beliefs.';
    }
  }
}
