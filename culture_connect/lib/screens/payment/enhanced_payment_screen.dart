import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/enhanced_payment_service.dart';
import 'package:culture_connect/widgets/payment/geolocation_payment_selector.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/screens/payment/payment_success_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Enhanced payment screen with Material Design 3 and provider integration
class EnhancedPaymentScreen extends ConsumerStatefulWidget {
  final Booking booking;
  final String userEmail;
  final String userName;
  final String? userPhone;

  const EnhancedPaymentScreen({
    super.key,
    required this.booking,
    required this.userEmail,
    required this.userName,
    this.userPhone,
  });

  @override
  ConsumerState<EnhancedPaymentScreen> createState() => _EnhancedPaymentScreenState();
}

class _EnhancedPaymentScreenState extends ConsumerState<EnhancedPaymentScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _progressController;
  
  PaymentMethodType? _selectedPaymentMethod;
  bool _isProcessing = false;
  bool _isInitializing = true;
  String? _errorMessage;
  GeolocationData? _geolocationData;
  List<PaymentMethodType> _availablePaymentMethods = [];

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );
    _progressController = AnimationController(
      duration: AppTheme.longAnimation,
      vsync: this,
    );

    _initializePaymentOptions();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  /// Initialize payment options and geolocation data
  Future<void> _initializePaymentOptions() async {
    try {
      // TODO: Get payment initialization data from enhanced payment service
      // This would call the backend to get available payment methods
      // and geolocation-based recommendations
      
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      // Mock data for demonstration
      setState(() {
        _geolocationData = const GeolocationData(
          countryCode: 'NG',
          countryName: 'Nigeria',
          region: 'Africa',
          city: 'Lagos',
          latitude: 6.5244,
          longitude: 3.3792,
          confidence: 0.9,
          isVpnDetected: false,
          recommendedProvider: 'paystack',
        );
        
        _availablePaymentMethods = [
          PaymentMethodType.card,
          PaymentMethodType.bankTransfer,
          PaymentMethodType.ussd,
          PaymentMethodType.crypto,
        ];
        
        _selectedPaymentMethod = PaymentMethodType.card; // Default to recommended
        _isInitializing = false;
      });

      _slideController.forward();

    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to initialize payment options: $e';
        _isInitializing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Complete Payment',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator
            LinearProgressIndicator(
              value: _isProcessing ? null : 0.7,
              backgroundColor: theme.colorScheme.surfaceVariant,
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.primary,
              ),
            ),

            // Main content
            Expanded(
              child: _isInitializing
                  ? _buildInitializingState(theme)
                  : _errorMessage != null
                      ? _buildErrorState(theme)
                      : _buildPaymentContent(theme),
            ),

            // Action buttons
            if (!_isInitializing && _errorMessage == null)
              _buildActionButtons(theme),
          ],
        ),
      ),
    );
  }

  /// Build initializing state
  Widget _buildInitializingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const LoadingIndicator(),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Initializing payment options...',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            'Detecting your location for optimal payment methods',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(128),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              'Payment Setup Failed',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              _errorMessage!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(179),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingLarge),
            FilledButton(
              onPressed: () {
                setState(() {
                  _errorMessage = null;
                  _isInitializing = true;
                });
                _initializePaymentOptions();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build main payment content
  Widget _buildPaymentContent(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Booking summary
          _buildBookingSummary(theme)
              .animate(controller: _slideController)
              .slideY(begin: 0.3, end: 0)
              .fadeIn(),

          const SizedBox(height: AppTheme.spacingLarge),

          // Payment method selector
          GeolocationPaymentSelector(
            geolocationData: _geolocationData,
            availablePaymentMethods: _availablePaymentMethods,
            selectedPaymentMethod: _selectedPaymentMethod,
            onPaymentMethodSelected: _onPaymentMethodSelected,
            isLoading: false,
          )
              .animate(controller: _slideController)
              .slideY(begin: 0.3, end: 0)
              .fadeIn(delay: const Duration(milliseconds: 200)),

          const SizedBox(height: AppTheme.spacingLarge),

          // Processing indicator
          if (_isProcessing)
            _buildProcessingIndicator(theme)
                .animate()
                .fadeIn()
                .slideY(begin: 0.2, end: 0),
        ],
      ),
    );
  }

  /// Build booking summary card
  Widget _buildBookingSummary(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        side: BorderSide(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Booking Summary',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Experience',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(179),
                  ),
                ),
                Text(
                  widget.booking.experienceId,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Participants',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(179),
                  ),
                ),
                Text(
                  '${widget.booking.participantCount}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const Divider(height: AppTheme.spacingMedium),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Amount',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '\$${widget.booking.totalAmount.toStringAsFixed(2)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build processing indicator
  Widget _buildProcessingIndicator(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withAlpha(51),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Text(
              'Processing your payment securely...',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withAlpha(51),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _isProcessing ? null : () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            flex: 2,
            child: FilledButton(
              onPressed: _isProcessing || _selectedPaymentMethod == null
                  ? null
                  : _processPayment,
              child: _isProcessing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text('Pay \$${widget.booking.totalAmount.toStringAsFixed(2)}'),
            ),
          ),
        ],
      ),
    );
  }

  /// Handle payment method selection
  void _onPaymentMethodSelected(PaymentMethodType method) {
    HapticFeedback.lightImpact();
    setState(() {
      _selectedPaymentMethod = method;
    });
  }

  /// Process payment
  Future<void> _processPayment() async {
    if (_selectedPaymentMethod == null) return;

    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    _progressController.forward();

    try {
      // TODO: Use EnhancedPaymentService to process payment
      // final result = await ref.read(enhancedPaymentServiceProvider).processPayment(
      //   context: context,
      //   booking: widget.booking,
      //   userEmail: widget.userEmail,
      //   userName: widget.userName,
      //   userPhone: widget.userPhone,
      //   preferredMethod: _selectedPaymentMethod,
      // );

      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 3));

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => PaymentSuccessScreen(
              transactionReference: 'CC_${DateTime.now().millisecondsSinceEpoch}',
              amount: widget.booking.totalAmount,
              paymentMethod: _selectedPaymentMethod!,
              booking: widget.booking,
            ),
          ),
        );
      }

    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isProcessing = false;
        });
        _progressController.reset();
      }
    }
  }
}
