import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import 'package:culture_connect/models/booking_model.dart';
import 'package:culture_connect/providers/booking_provider.dart';

/// A screen that displays the details of a booking for a guide.
class BookingDetailScreen extends ConsumerStatefulWidget {
  final String bookingId;

  const BookingDetailScreen({
    super.key,
    required this.bookingId,
  });

  @override
  ConsumerState<BookingDetailScreen> createState() =>
      _BookingDetailScreenState();
}

class _BookingDetailScreenState extends ConsumerState<BookingDetailScreen> {
  final TextEditingController _rejectionReasonController =
      TextEditingController();
  final TextEditingController _refundReasonController = TextEditingController();
  final TextEditingController _refundAmountController = TextEditingController();
  final TextEditingController _notificationMessageController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    // Load the booking when the screen is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(bookingNotifierProvider.notifier).loadBooking(widget.bookingId);
    });
  }

  @override
  void dispose() {
    _rejectionReasonController.dispose();
    _refundReasonController.dispose();
    _refundAmountController.dispose();
    _notificationMessageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bookingAsync = ref.watch(bookingNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Details'),
      ),
      body: bookingAsync.when(
        data: (booking) {
          if (booking == null) {
            return const Center(
              child: Text('Booking not found'),
            );
          }
          return _buildBookingDetails(context, booking);
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildBookingDetails(BuildContext context, BookingModel booking) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM d, yyyy');

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status Badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getStatusColor(booking.status),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              booking.status.displayName,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Experience Info
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Experience',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (booking.experience.imageUrl != null)
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            booking.experience.imageUrl!,
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                          ),
                        ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              booking.experience.title,
                              style: theme.textTheme.titleMedium,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              booking.experience.category,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${booking.experience.durationMinutes ~/ 60}h ${booking.experience.durationMinutes % 60}min',
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Customer Info
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Customer',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 24,
                        backgroundImage: booking.customer.profilePicture != null
                            ? NetworkImage(booking.customer.profilePicture!)
                            : null,
                        child: booking.customer.profilePicture == null
                            ? Text(booking.customer.name[0])
                            : null,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              booking.customer.name,
                              style: theme.textTheme.titleMedium,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              booking.customer.email,
                              style: theme.textTheme.bodyMedium,
                            ),
                            if (booking.customer.phone != null) ...[
                              const SizedBox(height: 4),
                              Text(
                                booking.customer.phone!,
                                style: theme.textTheme.bodyMedium,
                              ),
                            ],
                          ],
                        ),
                      ),
                      if (booking.customer.rating != null)
                        Row(
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              booking.customer.rating!.toStringAsFixed(1),
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Booking Details
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Booking Details',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildDetailRow(
                    'Booking Date',
                    dateFormat.format(booking.bookingDate),
                    theme,
                  ),
                  _buildDetailRow(
                    'Experience Date',
                    dateFormat.format(booking.experienceDate),
                    theme,
                  ),
                  _buildDetailRow(
                    'Start Time',
                    booking.startTime,
                    theme,
                  ),
                  _buildDetailRow(
                    'Participants',
                    booking.participantCount.toString(),
                    theme,
                  ),
                  if (booking.specialRequirements != null)
                    _buildDetailRow(
                      'Special Requirements',
                      booking.specialRequirements!,
                      theme,
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Payment Info
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Payment',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildDetailRow(
                    'Status',
                    booking.payment.status.displayName,
                    theme,
                  ),
                  _buildDetailRow(
                    'Amount',
                    '${booking.payment.currency} ${booking.payment.amount.toStringAsFixed(2)}',
                    theme,
                  ),
                  if (booking.payment.transactionId != null)
                    _buildDetailRow(
                      'Transaction ID',
                      booking.payment.transactionId!,
                      theme,
                    ),
                  if (booking.payment.paymentMethod != null)
                    _buildDetailRow(
                      'Payment Method',
                      booking.payment.paymentMethod!,
                      theme,
                    ),
                  if (booking.payment.paymentDate != null)
                    _buildDetailRow(
                      'Payment Date',
                      dateFormat.format(booking.payment.paymentDate!),
                      theme,
                    ),
                  if (booking.payment.refundAmount != null) ...[
                    _buildDetailRow(
                      'Refund Amount',
                      '${booking.payment.currency} ${booking.payment.refundAmount!.toStringAsFixed(2)}',
                      theme,
                    ),
                    if (booking.payment.refundDate != null)
                      _buildDetailRow(
                        'Refund Date',
                        dateFormat.format(booking.payment.refundDate!),
                        theme,
                      ),
                    if (booking.payment.refundReason != null)
                      _buildDetailRow(
                        'Refund Reason',
                        booking.payment.refundReason!,
                        theme,
                      ),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Status-specific information
          if (booking.status == BookingStatus.rejected &&
              booking.rejectionReason != null)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Rejection Reason',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(booking.rejectionReason!),
                  ],
                ),
              ),
            ),
          if (booking.status == BookingStatus.cancelled &&
              booking.cancellationReason != null)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Cancellation Reason',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(booking.cancellationReason!),
                  ],
                ),
              ),
            ),
          const SizedBox(height: 24),

          // Action Buttons
          _buildActionButtons(booking),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BookingModel booking) {
    // Different actions based on booking status
    if (booking.status == BookingStatus.pending) {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _approveBooking(booking.id),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Approve'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton(
                  onPressed: () => _showRejectDialog(booking.id),
                  child: const Text('Reject'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          OutlinedButton.icon(
            onPressed: () => _showSendNotificationDialog(booking.id),
            icon: const Icon(Icons.message),
            label: const Text('Contact Customer'),
          ),
        ],
      );
    } else if (booking.status == BookingStatus.approved) {
      final now = DateTime.now();
      final isInPast = booking.experienceDate.isBefore(now);

      return Column(
        children: [
          if (isInPast) ...[
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _completeBooking(booking.id),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Mark as Completed'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _markAsNoShow(booking.id),
                    child: const Text('Mark as No-Show'),
                  ),
                ),
              ],
            ),
          ] else ...[
            OutlinedButton.icon(
              onPressed: () => _showSendNotificationDialog(booking.id),
              icon: const Icon(Icons.message),
              label: const Text('Contact Customer'),
            ),
          ],
          const SizedBox(height: 16),
          if (booking.payment.status == PaymentStatus.paid)
            OutlinedButton.icon(
              onPressed: () =>
                  _showRefundDialog(booking.id, booking.payment.amount),
              icon: const Icon(Icons.money_off),
              label: const Text('Process Refund'),
            ),
        ],
      );
    } else if (booking.status == BookingStatus.completed) {
      return Column(
        children: [
          if (booking.payment.status == PaymentStatus.paid &&
              booking.payment.refundAmount == null)
            OutlinedButton.icon(
              onPressed: () =>
                  _showRefundDialog(booking.id, booking.payment.amount),
              icon: const Icon(Icons.money_off),
              label: const Text('Process Refund'),
            ),
        ],
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  void _approveBooking(String bookingId) async {
    try {
      await ref
          .read(bookingNotifierProvider.notifier)
          .approveBooking(bookingId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Booking approved successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  void _showRejectDialog(String bookingId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Booking'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejecting this booking:'),
            const SizedBox(height: 16),
            TextField(
              controller: _rejectionReasonController,
              decoration: const InputDecoration(
                labelText: 'Rejection Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _rejectBooking(bookingId, _rejectionReasonController.text);
            },
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  void _rejectBooking(String bookingId, String rejectionReason) async {
    if (rejectionReason.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide a rejection reason')),
      );
      return;
    }

    try {
      await ref
          .read(bookingNotifierProvider.notifier)
          .rejectBooking(bookingId, rejectionReason);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Booking rejected successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  void _completeBooking(String bookingId) async {
    try {
      await ref
          .read(bookingNotifierProvider.notifier)
          .completeBooking(bookingId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Booking marked as completed')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  void _markAsNoShow(String bookingId) async {
    try {
      await ref.read(bookingNotifierProvider.notifier).markAsNoShow(bookingId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Booking marked as no-show')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  void _showRefundDialog(String bookingId, double maxAmount) {
    _refundAmountController.text = maxAmount.toStringAsFixed(2);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Process Refund'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _refundAmountController,
              decoration: const InputDecoration(
                labelText: 'Refund Amount',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _refundReasonController,
              decoration: const InputDecoration(
                labelText: 'Refund Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processRefund(
                bookingId,
                double.tryParse(_refundAmountController.text) ?? maxAmount,
                _refundReasonController.text,
              );
            },
            child: const Text('Process Refund'),
          ),
        ],
      ),
    );
  }

  void _processRefund(
      String bookingId, double refundAmount, String refundReason) async {
    if (refundReason.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide a refund reason')),
      );
      return;
    }

    try {
      await ref
          .read(bookingNotifierProvider.notifier)
          .processRefund(bookingId, refundAmount, refundReason);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Refund processed successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  void _showSendNotificationDialog(String bookingId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Customer'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Send a message to the customer:'),
            const SizedBox(height: 16),
            TextField(
              controller: _notificationMessageController,
              decoration: const InputDecoration(
                labelText: 'Message',
                border: OutlineInputBorder(),
              ),
              maxLines: 5,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _sendNotification(bookingId, _notificationMessageController.text);
            },
            child: const Text('Send'),
          ),
        ],
      ),
    );
  }

  void _sendNotification(String bookingId, String message) async {
    if (message.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a message')),
      );
      return;
    }

    try {
      await ref
          .read(bookingNotifierProvider.notifier)
          .sendCustomerNotification(bookingId, message);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Message sent successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  Color _getStatusColor(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return Colors.orange;
      case BookingStatus.approved:
        return Colors.blue;
      case BookingStatus.rejected:
        return Colors.red;
      case BookingStatus.cancelled:
        return Colors.red[300]!;
      case BookingStatus.completed:
        return Colors.green;
      case BookingStatus.noShow:
        return Colors.grey;
    }
  }
}
