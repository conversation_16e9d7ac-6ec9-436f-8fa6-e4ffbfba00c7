// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/achievement/achievement.dart';
import 'package:culture_connect/models/achievement/user_achievement.dart';
import 'package:culture_connect/providers/achievement_provider.dart';
import 'package:culture_connect/widgets/achievement/achievement_card.dart';

import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Screen for displaying user achievements and progress
class AchievementsScreen extends ConsumerStatefulWidget {
  const AchievementsScreen({super.key});

  @override
  ConsumerState<AchievementsScreen> createState() => _AchievementsScreenState();
}

class _AchievementsScreenState extends ConsumerState<AchievementsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  AchievementCategory _selectedCategory = AchievementCategory.travelBooking;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: AchievementCategory.values.length,
      vsync: this,
    );

    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _selectedCategory = AchievementCategory.values[_tabController.index];
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final achievementsAsync = ref.watch(userAchievementsProvider);
    final achievementStats = ref.watch(achievementStatsProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Achievements',
        showBackButton: true,
      ),
      body: achievementsAsync.when(
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Failed to load achievements',
                style: theme.textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(userAchievementsProvider);
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
        data: (achievements) => Column(
          children: [
            // Stats header
            _buildStatsHeader(theme, achievements, achievementStats),

            // Category tabs
            _buildCategoryTabs(theme),

            // Achievement list
            Expanded(
              child: _buildAchievementList(achievements),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsHeader(
    ThemeData theme,
    List<UserAchievement> achievements,
    Map<String, dynamic> stats,
  ) {
    final unlockedCount = achievements.where((a) => a.isUnlocked).length;
    final totalCount = achievements.length;
    final progressPercentage =
        totalCount > 0 ? (unlockedCount / totalCount) : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withAlpha(204),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withAlpha(77),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Achievement Progress',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '$unlockedCount of $totalCount unlocked',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: Colors.white.withAlpha(230),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 80,
                height: 80,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    SizedBox(
                      width: 60,
                      height: 60,
                      child: CircularProgressIndicator(
                        value: progressPercentage,
                        strokeWidth: 6,
                        backgroundColor: Colors.grey.withAlpha(77),
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          AppTheme.primaryColor,
                        ),
                      ),
                    ),
                    Text(
                      '${(progressPercentage * 100).round()}%',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Quick stats
          Row(
            children: [
              _buildStatItem(
                theme,
                'Recent',
                _getRecentAchievements(achievements).length.toString(),
                Icons.schedule,
              ),
              const SizedBox(width: 16),
              _buildStatItem(
                theme,
                'In Progress',
                achievements.where((a) => a.isInProgress).length.toString(),
                Icons.trending_up,
              ),
              const SizedBox(width: 16),
              _buildStatItem(
                theme,
                'Ready',
                achievements.where((a) => a.canUnlock).length.toString(),
                Icons.star,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    ThemeData theme,
    String label,
    String value,
    IconData icon,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withAlpha(51),
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: theme.textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: theme.textTheme.labelSmall?.copyWith(
                color: Colors.white.withAlpha(230),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryTabs(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: theme.colorScheme.onSurfaceVariant,
        indicatorColor: AppTheme.primaryColor,
        indicatorWeight: 3,
        tabs: AchievementCategory.values.map((category) {
          return Tab(
            text: _getCategoryDisplayName(category),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildAchievementList(List<UserAchievement> achievements) {
    final filteredAchievements = achievements
        .where((a) => a.achievement.category == _selectedCategory)
        .toList();

    // Sort achievements: unlocked first, then by progress
    filteredAchievements.sort((a, b) {
      if (a.isUnlocked && !b.isUnlocked) return -1;
      if (!a.isUnlocked && b.isUnlocked) return 1;
      return b.progress.compareTo(a.progress);
    });

    if (filteredAchievements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No achievements in this category yet',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Start using CultureConnect to unlock achievements!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.only(top: 16, bottom: 32),
      itemCount: filteredAchievements.length,
      itemBuilder: (context, index) {
        final userAchievement = filteredAchievements[index];

        return AchievementCard(
          userAchievement: userAchievement,
          onTap: () => _showAchievementDetails(userAchievement),
        );
      },
    );
  }

  void _showAchievementDetails(UserAchievement userAchievement) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: AchievementCard(
            userAchievement: userAchievement,
            isCompact: false,
          ),
        ),
      ),
    );
  }

  List<UserAchievement> _getRecentAchievements(
      List<UserAchievement> achievements) {
    final now = DateTime.now();
    return achievements.where((a) {
      if (a.unlockedAt == null) return false;
      return now.difference(a.unlockedAt!).inDays <= 7;
    }).toList();
  }

  String _getCategoryDisplayName(AchievementCategory category) {
    switch (category) {
      case AchievementCategory.travelBooking:
        return 'Travel';
      case AchievementCategory.serviceUsage:
        return 'Services';
      case AchievementCategory.loyaltyProgram:
        return 'Loyalty';
      case AchievementCategory.safetyFeatures:
        return 'Safety';
      case AchievementCategory.exploration:
        return 'Explorer';
      case AchievementCategory.social:
        return 'Social';
    }
  }
}
