import 'package:flutter/material.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/payment/webview_payment_integration_service.dart';
import 'package:culture_connect/services/payment_providers/modern_paystack_provider.dart';
import 'package:culture_connect/services/payment_providers/real_stripe_provider.dart';
import 'package:culture_connect/services/payment_providers/busha_payment_provider.dart';
import 'package:culture_connect/services/payment_api_service.dart';
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/services/mascot_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Example implementation of WebView payment integration
/// 
/// This example shows how to integrate the WebView payment system
/// into existing payment confirmation screens
class WebViewPaymentIntegrationExample extends StatefulWidget {
  final Booking booking;
  final String userEmail;
  final String userName;
  final String userPhone;

  const WebViewPaymentIntegrationExample({
    super.key,
    required this.booking,
    required this.userEmail,
    required this.userName,
    required this.userPhone,
  });

  @override
  State<WebViewPaymentIntegrationExample> createState() =>
      _WebViewPaymentIntegrationExampleState();
}

class _WebViewPaymentIntegrationExampleState
    extends State<WebViewPaymentIntegrationExample> {
  late WebViewPaymentIntegrationService _integrationService;
  bool _isProcessing = false;
  String? _errorMessage;
  PaymentResult? _paymentResult;

  @override
  void initState() {
    super.initState();
    _initializePaymentService();
  }

  void _initializePaymentService() {
    // Initialize all required services
    final loggingService = LoggingService();
    final apiService = PaymentApiService(loggingService: loggingService);
    
    // Initialize payment providers
    final paystackProvider = ModernPaystackProvider(
      loggingService: loggingService,
    );
    
    final stripeProvider = RealStripeProvider(
      loggingService: loggingService,
    );
    
    final bushaProvider = BushaPaymentProvider(
      loggingService: loggingService,
    );

    // Initialize supporting services
    final achievementService = AchievementService(
      prefs: null, // TODO: Inject SharedPreferences
      analyticsService: null, // TODO: Inject AnalyticsService
      loggingService: loggingService,
      notificationService: null, // TODO: Inject NotificationService
    );
    
    final mascotService = MascotService(loggingService);
    final analyticsService = AnalyticsService();

    // Create integration service
    _integrationService = WebViewPaymentIntegrationService(
      paystackProvider: paystackProvider,
      stripeProvider: stripeProvider,
      bushaProvider: bushaProvider,
      apiService: apiService,
      achievementService: achievementService,
      mascotService: mascotService,
      analyticsService: analyticsService,
      loggingService: loggingService,
    );
  }

  Future<void> _processPayment() async {
    if (!mounted) return;

    setState(() {
      _isProcessing = true;
      _errorMessage = null;
      _paymentResult = null;
    });

    try {
      // Initialize the integration service
      await _integrationService.initialize(
        authToken: 'your_jwt_token_here', // TODO: Get from auth service
      );

      // Mock geolocation data (in real app, get from location service)
      const geolocationData = GeolocationData(
        countryCode: 'NG',
        countryName: 'Nigeria',
        region: 'Africa',
        city: 'Lagos',
        latitude: 6.5244,
        longitude: 3.3792,
        confidence: 0.9,
        isVpnDetected: false,
        recommendedProvider: 'paystack',
      );

      // Process payment with WebView integration
      final result = await _integrationService.processPayment(
        context: context,
        booking: widget.booking,
        userEmail: widget.userEmail,
        userName: widget.userName,
        userPhone: widget.userPhone,
        geolocationData: geolocationData,
      );

      if (mounted) {
        setState(() {
          _paymentResult = result;
          _isProcessing = false;
        });

        if (result.isSuccess) {
          _showSuccessDialog();
        } else {
          _showErrorDialog(result.error ?? 'Payment failed');
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isProcessing = false;
        });
        _showErrorDialog(_errorMessage!);
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('Payment Successful'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Amount: ${_paymentResult?.currency} ${_paymentResult?.amount}'),
            Text('Reference: ${_paymentResult?.transactionReference}'),
            Text('Provider: ${_paymentResult?.provider?.name}'),
            if (_paymentResult?.receiptUrl != null)
              Text('Receipt: ${_paymentResult?.receiptUrl}'),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Return to previous screen
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('Payment Failed'),
          ],
        ),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Try Again'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Return to previous screen
            },
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _integrationService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('WebView Payment Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Booking Details',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text('Booking ID: ${widget.booking.id}'),
                    Text('Amount: \$${widget.booking.totalAmount}'),
                    Text('User: ${widget.userName}'),
                    Text('Email: ${widget.userEmail}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            if (_isProcessing)
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Processing payment...'),
                  ],
                ),
              )
            else
              ElevatedButton(
                onPressed: _processPayment,
                child: const Text('Process Payment with WebView'),
              ),
            if (_errorMessage != null) ...[
              const SizedBox(height: 16),
              Card(
                color: Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Error:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      Text(_errorMessage!),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Usage in existing payment confirmation screens:
/// 
/// ```dart
/// // In your payment confirmation screen
/// final integrationService = WebViewPaymentIntegrationService(
///   // ... initialize with required services
/// );
/// 
/// final result = await integrationService.processPayment(
///   context: context,
///   booking: booking,
///   userEmail: userEmail,
///   userName: userName,
///   userPhone: userPhone,
///   geolocationData: geolocationData,
/// );
/// 
/// if (result.isSuccess) {
///   // Handle success
/// } else {
///   // Handle failure
/// }
/// ```
