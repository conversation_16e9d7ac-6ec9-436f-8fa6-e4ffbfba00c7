import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/enhanced_payment_service.dart';
import 'package:culture_connect/services/payment_api_service.dart';
import 'package:culture_connect/services/payment_providers/real_stripe_provider.dart';
import 'package:culture_connect/services/payment_providers/paystack_payment_provider.dart';
import 'package:culture_connect/services/payment_providers/busha_payment_provider.dart';
import 'package:culture_connect/providers/achievement_provider.dart'
    hide analyticsServiceProvider;
import 'package:culture_connect/services/mascot_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/providers/services_providers.dart'
    as services_providers;
import 'package:culture_connect/utils/payment_error_handler.dart';

/// Enhanced payment provider for Riverpod state management
final enhancedPaymentServiceProvider = Provider<EnhancedPaymentService>((ref) {
  final loggingService = ref.read(services_providers.loggingServiceProvider);

  return EnhancedPaymentService(
    apiService: PaymentApiService(loggingService: loggingService),
    stripeProvider: RealStripeProvider(loggingService: loggingService),
    paystackProvider: PaystackPaymentProvider(loggingService: loggingService),
    bushaProvider: BushaPaymentProvider(loggingService: loggingService),
    achievementService: ref.read(achievementServiceProvider),
    mascotService: ref.read(mascotServiceProvider),
    analyticsService: ref.read(analyticsServiceProvider),
    loggingService: loggingService,
  );
});

/// Payment error handler provider
final paymentErrorHandlerProvider = Provider<PaymentErrorHandler>((ref) {
  return PaymentErrorHandler(
    loggingService: ref.read(loggingServiceProvider),
  );
});

/// Payment state provider
final paymentStateProvider =
    StateNotifierProvider<PaymentStateNotifier, PaymentState>((ref) {
  return PaymentStateNotifier(
    enhancedPaymentService: ref.read(enhancedPaymentServiceProvider),
    errorHandler: ref.read(paymentErrorHandlerProvider),
  );
});

/// Payment method cache provider
final paymentMethodCacheProvider =
    StateProvider<Map<String, List<PaymentMethodType>>>((ref) {
  return {};
});

/// Provider configuration cache provider
final providerConfigCacheProvider =
    StateProvider<Map<PaymentProvider, Map<String, dynamic>>>((ref) {
  return {};
});

/// Payment state notifier for managing payment flows
class PaymentStateNotifier extends StateNotifier<PaymentState> {
  final EnhancedPaymentService _paymentService;
  final PaymentErrorHandler _errorHandler;

  PaymentStateNotifier({
    required EnhancedPaymentService enhancedPaymentService,
    required PaymentErrorHandler errorHandler,
  })  : _paymentService = enhancedPaymentService,
        _errorHandler = errorHandler,
        super(const PaymentState.initial());

  /// Initialize payment service
  Future<void> initialize({required String authToken}) async {
    try {
      state = const PaymentState.initializing();

      await _paymentService.initialize(authToken: authToken);

      state = const PaymentState.ready();
    } catch (e) {
      state = PaymentState.error(e.toString());
    }
  }

  /// Process payment with comprehensive error handling
  Future<void> processPayment({
    required BuildContext context,
    required Booking booking,
    required String userEmail,
    required String userName,
    String? userPhone,
    PaymentMethodType? preferredMethod,
  }) async {
    try {
      state = const PaymentState.processing();

      final result = await _paymentService.processPayment(
        context: context,
        booking: booking,
        userEmail: userEmail,
        userName: userName,
        userPhone: userPhone,
        preferredMethod: preferredMethod,
      );

      if (result.success) {
        state = PaymentState.success(
          transactionReference: result.transactionReference!,
          provider: result.provider!,
          receiptId: result.receiptId,
          processingTime: result.processingTime,
        );
      } else {
        state = PaymentState.failed(
          error: result.error ?? 'Payment failed',
          transactionReference: result.transactionReference,
        );
      }
    } catch (e) {
      // Handle error with comprehensive error handler
      // Check if context is still mounted before using it
      if (context.mounted) {
        final errorResult = await _errorHandler.handleError(
          error: e as Exception,
          operation: 'processPayment',
          context: context,
        );

        if (!errorResult.isResolved) {
          state = PaymentState.failed(
            error: errorResult.userMessage,
            fallbackOptions: errorResult.fallbackOptions,
          );
        }
      } else {
        // Context is no longer mounted, handle gracefully
        state = const PaymentState.failed(
          error: 'Payment operation was cancelled',
        );
      }
    }
  }

  /// Get available payment methods for location
  Future<List<PaymentMethodType>> getAvailablePaymentMethods({
    String? countryCode,
    bool useCache = true,
  }) async {
    final cacheKey = countryCode ?? 'default';

    // Check cache first
    if (useCache && _paymentMethodCache.containsKey(cacheKey)) {
      return _paymentMethodCache[cacheKey]!;
    }

    try {
      // TODO: Implement backend call to get available payment methods
      // This would call the backend API to get location-specific payment methods

      // Mock implementation for demonstration
      List<PaymentMethodType> methods;

      if (countryCode?.toLowerCase() == 'ng' ||
          countryCode?.toLowerCase() == 'ke') {
        // African countries - Paystack methods
        methods = [
          PaymentMethodType.card,
          PaymentMethodType.bankTransfer,
          PaymentMethodType.ussd,
          PaymentMethodType.mobileMoney,
          PaymentMethodType.crypto,
        ];
      } else {
        // Other countries - Stripe methods
        methods = [
          PaymentMethodType.card,
          PaymentMethodType.crypto,
        ];
      }

      // Cache the result
      _paymentMethodCache[cacheKey] = methods;

      return methods;
    } catch (e) {
      // Return default methods on error
      return [PaymentMethodType.card];
    }
  }

  /// Cache for payment methods by location
  final Map<String, List<PaymentMethodType>> _paymentMethodCache = {};

  /// Reset payment state
  void reset() {
    state = const PaymentState.initial();
  }

  /// Dispose resources
  @override
  void dispose() {
    _paymentService.dispose();
    super.dispose();
  }
}

/// Payment state sealed class
@immutable
sealed class PaymentState {
  const PaymentState();

  const factory PaymentState.initial() = PaymentInitial;
  const factory PaymentState.initializing() = PaymentInitializing;
  const factory PaymentState.ready() = PaymentReady;
  const factory PaymentState.processing() = PaymentProcessing;
  const factory PaymentState.success({
    required String transactionReference,
    required PaymentProvider provider,
    String? receiptId,
    Duration? processingTime,
  }) = PaymentSuccess;
  const factory PaymentState.failed({
    required String error,
    String? transactionReference,
    List<PaymentFallbackOption>? fallbackOptions,
  }) = PaymentFailed;
  const factory PaymentState.error(String message) = PaymentError;
}

/// Payment state implementations
class PaymentInitial extends PaymentState {
  const PaymentInitial();
}

class PaymentInitializing extends PaymentState {
  const PaymentInitializing();
}

class PaymentReady extends PaymentState {
  const PaymentReady();
}

class PaymentProcessing extends PaymentState {
  const PaymentProcessing();
}

class PaymentSuccess extends PaymentState {
  final String transactionReference;
  final PaymentProvider provider;
  final String? receiptId;
  final Duration? processingTime;

  const PaymentSuccess({
    required this.transactionReference,
    required this.provider,
    this.receiptId,
    this.processingTime,
  });
}

class PaymentFailed extends PaymentState {
  final String error;
  final String? transactionReference;
  final List<PaymentFallbackOption>? fallbackOptions;

  const PaymentFailed({
    required this.error,
    this.transactionReference,
    this.fallbackOptions,
  });
}

class PaymentError extends PaymentState {
  final String message;

  const PaymentError(this.message);
}

/// Extension for payment state checks
extension PaymentStateExtension on PaymentState {
  bool get isInitial => this is PaymentInitial;
  bool get isInitializing => this is PaymentInitializing;
  bool get isReady => this is PaymentReady;
  bool get isProcessing => this is PaymentProcessing;
  bool get isSuccess => this is PaymentSuccess;
  bool get isFailed => this is PaymentFailed;
  bool get isError => this is PaymentError;
  bool get isLoading => isInitializing || isProcessing;
}

/// Payment analytics provider for tracking payment metrics
final paymentAnalyticsProvider = Provider<PaymentAnalytics>((ref) {
  return PaymentAnalytics(
    analyticsService: ref.read(analyticsServiceProvider),
  );
});

/// Payment analytics service
class PaymentAnalytics {
  final AnalyticsService _analyticsService;

  PaymentAnalytics({
    required AnalyticsService analyticsService,
  }) : _analyticsService = analyticsService;

  /// Track payment method selection
  Future<void> trackPaymentMethodSelected({
    required PaymentMethodType method,
    required PaymentProvider provider,
    String? countryCode,
  }) async {
    await _analyticsService.logEvent(
      name: 'payment_method_selected',
      category: AnalyticsCategory.payment,
      parameters: {
        'payment_method': method.name,
        'provider': provider.name,
        if (countryCode != null) 'country_code': countryCode,
      },
    );
  }

  /// Track payment initialization
  Future<void> trackPaymentInitialized({
    required double amount,
    required String currency,
    required PaymentProvider provider,
  }) async {
    await _analyticsService.logEvent(
      name: 'payment_initialized',
      category: AnalyticsCategory.payment,
      parameters: {
        'amount': amount,
        'currency': currency,
        'provider': provider.name,
      },
    );
  }

  /// Track payment completion
  Future<void> trackPaymentCompleted({
    required String transactionReference,
    required double amount,
    required PaymentProvider provider,
    required Duration processingTime,
  }) async {
    await _analyticsService.logEvent(
      name: 'payment_completed',
      category: AnalyticsCategory.payment,
      parameters: {
        'transaction_reference': transactionReference,
        'amount': amount,
        'provider': provider.name,
        'processing_time_ms': processingTime.inMilliseconds,
      },
    );
  }

  /// Track payment failure
  Future<void> trackPaymentFailed({
    required String error,
    required PaymentProvider provider,
    String? transactionReference,
  }) async {
    await _analyticsService.logEvent(
      name: 'payment_failed',
      category: AnalyticsCategory.payment,
      parameters: {
        'error': error,
        'provider': provider.name,
        if (transactionReference != null)
          'transaction_reference': transactionReference,
      },
    );
  }
}
